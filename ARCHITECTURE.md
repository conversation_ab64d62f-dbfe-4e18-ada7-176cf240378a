# GhostLayer Application Architecture

## Table of Contents
1. [Overview & Design Philosophy](#overview--design-philosophy)
2. [Technology Stack](#technology-stack)
3. [Application Architecture](#application-architecture)
4. [Text Processing Engine](#text-processing-engine)
5. [Design Patterns](#design-patterns)
6. [Component Hierarchy](#component-hierarchy)
7. [Data Flow](#data-flow)
8. [Key Architectural Decisions](#key-architectural-decisions)
9. [Performance Considerations](#performance-considerations)
10. [Security Architecture](#security-architecture)

## Overview & Design Philosophy

GhostLayer is a sophisticated AI text humanization application built with modern web technologies. The application follows several core design principles:

### Core Principles
- **Modularity**: Each component and processor is self-contained and reusable
- **Scalability**: Architecture supports easy addition of new languages and processing modes
- **Performance**: Client-side processing ensures fast response times and privacy
- **Extensibility**: Plugin-like architecture for text processors and language support
- **Reliability**: Comprehensive error handling and fallback mechanisms
- **User Experience**: Responsive design with real-time feedback and progress tracking

### Design Goals
1. **Zero-dependency text processing**: Core algorithms work without external APIs
2. **Multi-language support**: Extensible language processor architecture
3. **Real-time processing**: Immediate feedback and results
4. **Privacy-first**: All processing happens client-side
5. **Production-ready**: Optimized for both development and deployment

## Technology Stack

### Frontend Framework
- **Next.js 13+** with App Router
  - **Rationale**: Modern React framework with excellent performance, SEO, and developer experience
  - **App Router**: Provides better file-based routing and layout management
  - **Static Export**: Enables deployment to static hosting platforms like Netlify

### Styling & UI
- **Tailwind CSS**: Utility-first CSS framework for rapid development
- **Radix UI**: Unstyled, accessible UI primitives
- **shadcn/ui**: Pre-built components based on Radix UI
- **Lucide React**: Consistent icon library

### Language & Type Safety
- **TypeScript**: Full type safety across the application
- **Strict mode**: Enhanced type checking and error prevention
- **Custom type definitions**: Comprehensive type system for processing options and results

### State Management
- **React Context**: Global state management for user preferences and authentication
- **Local State**: Component-level state with React hooks
- **Session Storage**: Persistent user preferences and settings

### Authentication & Payments
- **NextAuth.js**: Flexible authentication library
- **Google OAuth**: Social authentication
- **LemonSqueezy**: Payment processing and subscription management

### Development & Build Tools
- **ESLint**: Code linting and quality enforcement
- **Prettier**: Code formatting
- **TypeScript Compiler**: Type checking and compilation
- **Next.js Build System**: Optimized production builds

## Application Architecture

### High-Level Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
├─────────────────────────────────────────────────────────────┤
│  Components/     │  Pages/        │  Layouts/              │
│  - TextInput     │  - Main App    │  - RootLayout          │
│  - OutputDisplay │  - Auth Pages  │  - AuthLayout          │
│  - ProcessingOpt │  - Settings    │                        │
├─────────────────────────────────────────────────────────────┤
│                    Business Logic Layer                     │
├─────────────────────────────────────────────────────────────┤
│  Text Processing Engine    │  Authentication    │  Utils    │
│  - Core Processor         │  - NextAuth.js     │  - Helpers │
│  - Language Processors    │  - Session Mgmt    │  - Types   │
│  - AI Detection          │  - User Management  │  - Config  │
├─────────────────────────────────────────────────────────────┤
│                      Data Layer                             │
├─────────────────────────────────────────────────────────────┤
│  Local Storage    │  Session Storage    │  External APIs    │
│  - User Prefs     │  - Temp Data       │  - Google OAuth   │
│  - Processing     │  - Form State      │  - LemonSqueezy   │
│    History        │                    │                   │
└─────────────────────────────────────────────────────────────┘
```

### Directory Structure

```
GhostLayer/
├── app/                          # Next.js App Router
│   ├── api/                      # API routes
│   │   ├── auth/                 # Authentication endpoints
│   │   ├── process/              # Text processing endpoint
│   │   ├── detect/               # AI detection endpoint
│   │   └── integrations/         # Third-party integrations
│   ├── auth/                     # Authentication pages
│   ├── globals.css               # Global styles
│   ├── layout.tsx                # Root layout component
│   └── page.tsx                  # Main application page
├── components/                   # React components
│   ├── ui/                       # shadcn/ui base components
│   ├── auth/                     # Authentication components
│   ├── processing/               # Text processing components
│   └── shared/                   # Shared/common components
├── lib/                          # Core business logic
│   ├── textProcessor/            # Text processing engine
│   │   ├── advancedHumanizer.ts  # Advanced humanization algorithm
│   │   ├── paraphraser.ts        # Basic paraphrasing logic
│   │   ├── styleOptimizer.ts     # Style optimization
│   │   ├── wordEmbeddings.ts     # Semantic word replacement
│   │   ├── languageProcessors/   # Language-specific processors
│   │   │   ├── vietnameseProcessor.ts
│   │   │   ├── spanishProcessor.ts
│   │   │   ├── frenchProcessor.ts
│   │   │   └── japaneseProcessor.ts
│   │   └── variationGenerator.ts # Text variation generation
│   ├── aiDetection/              # AI detection algorithms
│   │   └── detector.ts           # Main detection logic
│   ├── auth/                     # Authentication logic
│   ├── integrations/             # External service integrations
│   └── utils/                    # Utility functions
├── types/                        # TypeScript type definitions
├── hooks/                        # Custom React hooks
├── public/                       # Static assets
└── extension/                    # Browser extension code
```

## Text Processing Engine

### Architecture Overview

The text processing engine is the core of GhostLayer, designed with a modular, pipeline-based architecture that supports multiple processing strategies and languages.

### Processing Pipeline

```
Input Text
    ↓
┌─────────────────────────────────────────────────────────────┐
│                    Preprocessing Phase                      │
│  - Structure Analysis    - Language Detection              │
│  - Format Preservation   - Content Classification          │
└─────────────────────────────────────────────────────────────┘
    ↓
┌─────────────────────────────────────────────────────────────┐
│                  Language-Specific Processing               │
│  English: Paraphrasing → Style Optimization → Embeddings   │
│  Vietnamese: Cultural Context → Synonym Replacement        │
│  Spanish: Regional Variations → Formality Adjustment       │
│  French: Grammar Optimization → Cultural Adaptation        │
│  Japanese: Politeness Levels → Character Variations        │
└─────────────────────────────────────────────────────────────┘
    ↓
┌─────────────────────────────────────────────────────────────┐
│                  Advanced Humanization                     │
│  (Heavy Mode Only)                                         │
│  - Sentence Restructuring  - AI Pattern Removal           │
│  - Advanced Synonyms       - Natural Inconsistencies      │
│  - Humanization Techniques - Contraction Insertion        │
└─────────────────────────────────────────────────────────────┘
    ↓
┌─────────────────────────────────────────────────────────────┐
│                    Post-Processing                          │
│  - Structure Reconstruction  - Quality Validation          │
│  - Variation Generation      - Metrics Calculation         │
└─────────────────────────────────────────────────────────────┘
    ↓
Processed Text + Metrics
```

### Core Components

#### 1. Main Text Processor (`textProcessor.ts`)
- **Purpose**: Orchestrates the entire processing pipeline
- **Responsibilities**:
  - Language detection and routing
  - Structure preservation and reconstruction
  - Error handling and fallback mechanisms
  - Performance monitoring and metrics calculation

#### 2. Advanced Humanizer (`advancedHumanizer.ts`)
- **Purpose**: Implements sophisticated humanization for heavy mode
- **Key Features**:
  - Multi-pass processing (4 distinct phases)
  - Sentence structure analysis and restructuring
  - AI pattern detection and removal
  - Natural inconsistency injection
  - 80% replacement rate for maximum effectiveness

#### 3. Language Processors
- **Modular Design**: Each language has its own processor module
- **Consistent Interface**: All processors implement the same interface
- **Cultural Awareness**: Language-specific cultural and linguistic patterns
- **Regional Variations**: Support for regional dialects and variations

#### 4. AI Detection Engine (`detector.ts`)
- **Purpose**: Analyzes text for AI-generated patterns
- **Metrics**:
  - Sentence structure uniformity
  - Vocabulary variety and sophistication
  - Flow and rhythm analysis
  - Style consistency evaluation

### Processing Intensities

#### Light Mode (20% replacement)
- Minimal changes to preserve original structure
- Basic synonym replacement
- Conservative approach for sensitive content

#### Medium Mode (35% replacement)
- Balanced transformation with good readability
- Style optimization and moderate paraphrasing
- Suitable for most use cases

#### Heavy Mode (80% replacement)
- Maximum humanization with advanced techniques
- Complete sentence restructuring
- AI pattern removal and natural inconsistency injection
- Target: <30% AI detection rate

## Design Patterns

### 1. Factory Pattern
**Implementation**: Language processor selection
```typescript
function getLanguageProcessor(language: string): LanguageProcessor {
  switch (language) {
    case 'vi': return new VietnameseProcessor();
    case 'es': return new SpanishProcessor();
    case 'fr': return new FrenchProcessor();
    default: return new EnglishProcessor();
  }
}
```

### 2. Strategy Pattern
**Implementation**: Processing intensity strategies
```typescript
interface ProcessingStrategy {
  process(text: string, options: ProcessingOptions): string;
}

class LightProcessingStrategy implements ProcessingStrategy { ... }
class MediumProcessingStrategy implements ProcessingStrategy { ... }
class HeavyProcessingStrategy implements ProcessingStrategy { ... }
```

### 3. Pipeline Pattern
**Implementation**: Text processing pipeline
```typescript
const pipeline = [
  preprocessText,
  detectLanguage,
  applyLanguageProcessing,
  applyAdvancedHumanization,
  postprocessText
];

const result = pipeline.reduce((text, processor) => processor(text, options), inputText);
```

### 4. Provider Pattern
**Implementation**: Global state management
```typescript
export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <SessionProvider>
      <ThemeProvider>
        <ProcessingProvider>
          {children}
        </ProcessingProvider>
      </ThemeProvider>
    </SessionProvider>
  );
}
```

### 5. Observer Pattern
**Implementation**: Real-time processing updates
```typescript
class ProcessingManager {
  private observers: ProcessingObserver[] = [];
  
  subscribe(observer: ProcessingObserver) {
    this.observers.push(observer);
  }
  
  notifyProgress(progress: ProcessingProgress) {
    this.observers.forEach(observer => observer.onProgress(progress));
  }
}
```

### 6. Decorator Pattern
**Implementation**: Processing enhancement layers
```typescript
function withErrorHandling<T extends (...args: any[]) => any>(fn: T): T {
  return ((...args: any[]) => {
    try {
      return fn(...args);
    } catch (error) {
      console.warn(`Processing failed: ${error.message}`);
      return args[0]; // Return original text as fallback
    }
  }) as T;
}
```

## Component Hierarchy

### Root Level
```
App (layout.tsx)
├── Providers
│   ├── SessionProvider (NextAuth)
│   ├── ThemeProvider
│   └── ProcessingProvider
└── Main Application (page.tsx)
    ├── Header
    │   ├── Logo
    │   ├── Navigation
    │   └── UserMenu
    ├── MainContent
    │   ├── TextInput
    │   │   ├── InputArea
    │   │   ├── FileUpload
    │   │   └── ProcessButton
    │   ├── ProcessingOptions
    │   │   ├── IntensitySlider
    │   │   ├── StyleSelector
    │   │   └── LanguageSelector
    │   └── OutputDisplay
    │       ├── ResultText
    │       ├── MetricsDisplay
    │       └── ActionButtons
    └── Sidebar
        ├── DetectionScore
        ├── ProcessingHistory
        └── BatchProcessing
```

### Component Design Principles

#### 1. Single Responsibility
Each component has a single, well-defined purpose:
- `TextInput`: Handles text input and file uploads
- `ProcessingOptions`: Manages processing configuration
- `OutputDisplay`: Shows results and metrics

#### 2. Composition over Inheritance
Components are composed of smaller, reusable parts:
```typescript
function ProcessingOptions({ options, onChange }: ProcessingOptionsProps) {
  return (
    <div className="processing-options">
      <IntensitySlider value={options.intensity} onChange={handleIntensityChange} />
      <StyleSelector value={options.style} onChange={handleStyleChange} />
      <LanguageSelector value={options.language} onChange={handleLanguageChange} />
    </div>
  );
}
```

#### 3. Props Interface Design
Clear, typed interfaces for all component props:
```typescript
interface ProcessingOptionsProps {
  options: ProcessingOptions;
  onChange: (options: ProcessingOptions) => void;
  disabled?: boolean;
  className?: string;
}
```

## Data Flow

### Unidirectional Data Flow

```
User Input
    ↓
Component State Update
    ↓
Processing Options Change
    ↓
Text Processing Trigger
    ↓
Processing Engine
    ↓
Result State Update
    ↓
UI Re-render
    ↓
User Feedback
```

### State Management Strategy

#### 1. Local Component State
- Form inputs and temporary UI state
- Processing status and progress
- Component-specific configurations

#### 2. Context-Based Global State
- User authentication status
- Global processing preferences
- Theme and UI preferences

#### 3. Persistent Storage
- User settings in localStorage
- Processing history in sessionStorage
- Authentication tokens in secure storage

### Event Flow

#### 1. User Interactions
```typescript
// User types in text input
onTextChange(newText) → updateInputText(newText) → triggerProcessing()

// User changes processing options
onOptionsChange(newOptions) → updateOptions(newOptions) → reprocessIfNeeded()

// User clicks process button
onProcessClick() → validateInput() → startProcessing() → updateUI()
```

#### 2. Processing Events
```typescript
// Processing pipeline
startProcessing() → 
  showProgress() → 
  processText() → 
  updateResults() → 
  hideProgress() → 
  showCompletion()
```

## Key Architectural Decisions

### 1. Client-Side Processing
**Decision**: Perform all text processing on the client side
**Rationale**: 
- Privacy: User data never leaves their device
- Performance: No network latency for processing
- Scalability: No server resources required for processing
- Offline capability: Works without internet connection

### 2. Static Site Generation
**Decision**: Use Next.js static export for deployment
**Rationale**:
- Cost-effective: Can be hosted on free static hosting platforms
- Performance: Fast loading times with CDN distribution
- Security: No server-side attack surface
- Reliability: High availability with static hosting

### 3. Modular Language Processing
**Decision**: Separate processor for each language
**Rationale**:
- Maintainability: Each language can be developed independently
- Extensibility: Easy to add new languages
- Performance: Only load processors for detected languages
- Quality: Language-specific optimizations and cultural awareness

### 4. TypeScript Throughout
**Decision**: Use TypeScript for all code
**Rationale**:
- Type Safety: Catch errors at compile time
- Developer Experience: Better IDE support and autocomplete
- Maintainability: Self-documenting code with type definitions
- Refactoring: Safe refactoring with type checking

### 5. Component-Based Architecture
**Decision**: Break UI into small, reusable components
**Rationale**:
- Reusability: Components can be used across different pages
- Testability: Easier to test individual components
- Maintainability: Changes are isolated to specific components
- Performance: Efficient re-rendering with React's reconciliation

## Performance Considerations

### 1. Processing Optimization
- **Lazy Loading**: Language processors loaded on demand
- **Memoization**: Cache processing results for identical inputs
- **Web Workers**: Consider moving heavy processing to background threads
- **Streaming**: Process large texts in chunks for better responsiveness

### 2. Bundle Optimization
- **Code Splitting**: Separate bundles for different features
- **Tree Shaking**: Remove unused code from bundles
- **Dynamic Imports**: Load components and processors on demand
- **Asset Optimization**: Compress images and optimize fonts

### 3. Runtime Performance
- **React Optimization**: Use React.memo and useMemo for expensive operations
- **Virtual Scrolling**: For large text processing results
- **Debouncing**: Limit processing frequency during typing
- **Progressive Enhancement**: Core functionality works without JavaScript

## Security Architecture

### 1. Client-Side Security
- **Input Sanitization**: Clean user input before processing
- **XSS Prevention**: Escape output when rendering HTML
- **Content Security Policy**: Restrict resource loading
- **Secure Headers**: Implement security headers in deployment

### 2. Authentication Security
- **OAuth 2.0**: Secure authentication with Google
- **JWT Tokens**: Secure session management
- **CSRF Protection**: Prevent cross-site request forgery
- **Session Timeout**: Automatic logout after inactivity

### 3. Data Privacy
- **Local Processing**: No data sent to external servers
- **Minimal Data Collection**: Only collect necessary user information
- **Secure Storage**: Encrypt sensitive data in local storage
- **Privacy Controls**: User control over data retention and deletion

This architecture provides a solid foundation for a scalable, maintainable, and performant text humanization application while ensuring user privacy and security.
