import { NextRequest, NextResponse } from 'next/server';
import mammoth from 'mammoth';
import * as pdfParse from 'pdf-parse';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    
    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    const fileType = file.type;
    const fileName = file.name.toLowerCase();
    let extractedText = '';

    // Get file buffer
    const arrayBuffer = await file.arrayBuffer();

    if (fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' || fileName.endsWith('.docx')) {
      // Extract text from DOCX using mammoth
      const result = await mammoth.extractRawText({ arrayBuffer });
      extractedText = result.value.replace(/\s+/g, ' ').trim();
      
    } else if (fileType === 'application/pdf' || fileName.endsWith('.pdf')) {
      // Extract text from PDF using pdf-parse
      try {
        const buffer = Buffer.from(arrayBuffer);

        // Add validation for buffer size and content
        if (buffer.length === 0) {
          throw new Error('PDF file appears to be empty or corrupted');
        }

        // Check if buffer starts with PDF signature
        const pdfSignature = buffer.slice(0, 4).toString();
        if (pdfSignature !== '%PDF') {
          throw new Error('File does not appear to be a valid PDF');
        }

        console.log(`Processing PDF: ${fileName}, size: ${buffer.length} bytes`);

        const data = await pdfParse(buffer, {
          // Add options to handle problematic PDFs
          max: 0, // No page limit
          version: 'v1.10.100' // Use specific version for consistency
        });

        if (!data || !data.text) {
          throw new Error('PDF parsing returned no text content');
        }

        extractedText = data.text.replace(/\s+/g, ' ').trim();
        console.log(`PDF text extracted successfully: ${extractedText.length} characters`);

      } catch (pdfError) {
        console.error('PDF processing error:', pdfError);
        throw new Error(`PDF processing failed: ${pdfError instanceof Error ? pdfError.message : 'Unknown PDF error'}`);
      }
      
    } else if (fileName.endsWith('.rtf')) {
      // Extract text from RTF
      const decoder = new TextDecoder('utf-8');
      const content = decoder.decode(arrayBuffer);
      extractedText = content
        .replace(/\\[a-z]+\d*\s?/g, '') // Remove RTF control words
        .replace(/[{}]/g, '') // Remove braces
        .replace(/\\\\/g, '\\') // Unescape backslashes
        .replace(/\\'/g, "'") // Unescape quotes
        .replace(/\s+/g, ' ') // Normalize whitespace
        .trim();
        
    } else if (fileName.endsWith('.odt')) {
      // Extract text from ODT
      const decoder = new TextDecoder('utf-8');
      const content = decoder.decode(arrayBuffer);
      
      // Look for text content in ODT XML structure
      const paragraphMatches = content.match(/<text:p[^>]*>([^<]+)<\/text:p>/g);
      if (paragraphMatches) {
        extractedText = paragraphMatches
          .map(match => match.replace(/<[^>]+>/g, ''))
          .join('\n')
          .replace(/\s+/g, ' ')
          .trim();
      } else {
        // Fallback: extract any readable text
        const readableText = content.match(/[a-zA-Z0-9\s.,!?;:'"()-]{10,}/g);
        if (readableText) {
          extractedText = readableText
            .join(' ')
            .replace(/\s+/g, ' ')
            .trim();
        }
      }
      
    } else if (fileName.endsWith('.txt') || fileName.endsWith('.md')) {
      // Plain text files
      const decoder = new TextDecoder('utf-8');
      extractedText = decoder.decode(arrayBuffer).trim();
      
    } else if (fileName.endsWith('.json')) {
      // JSON files
      const decoder = new TextDecoder('utf-8');
      const content = decoder.decode(arrayBuffer);
      try {
        const parsed = JSON.parse(content);
        extractedText = extractTextFromJSON(parsed);
      } catch {
        extractedText = content;
      }
      
    } else if (fileName.endsWith('.csv')) {
      // CSV files
      const decoder = new TextDecoder('utf-8');
      const content = decoder.decode(arrayBuffer);
      extractedText = content
        .split('\n')
        .map(line => line.split(',').join(' '))
        .join('\n')
        .replace(/\s+/g, ' ')
        .trim();
        
    } else if (fileName.endsWith('.html') || fileName.endsWith('.xml')) {
      // HTML/XML files
      const decoder = new TextDecoder('utf-8');
      const content = decoder.decode(arrayBuffer);
      extractedText = content
        .replace(/<[^>]+>/g, ' ') // Remove HTML/XML tags
        .replace(/\s+/g, ' ')
        .trim();
        
    } else {
      return NextResponse.json({ error: 'Unsupported file type' }, { status: 400 });
    }

    if (!extractedText || extractedText.length < 10) {
      return NextResponse.json({ error: 'No readable text found in file' }, { status: 400 });
    }

    return NextResponse.json({ text: extractedText });

  } catch (error) {
    console.error('Text extraction error:', error);

    // Ensure we always return a proper JSON response
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred during text extraction';

    // Log additional context for debugging
    console.error('Error context:', {
      fileName: fileName || 'unknown',
      fileType: fileType || 'unknown',
      fileSize: arrayBuffer?.byteLength || 0,
      timestamp: new Date().toISOString()
    });

    return NextResponse.json(
      {
        error: `Failed to extract text from ${fileName || 'file'}: ${errorMessage}`,
        details: {
          fileName: fileName || 'unknown',
          fileType: fileType || 'unknown',
          timestamp: new Date().toISOString()
        }
      },
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}

// Helper function to extract text from JSON objects
function extractTextFromJSON(obj: any, depth = 0): string {
  if (depth > 10) return ''; // Prevent infinite recursion
  
  let text = '';
  
  if (typeof obj === 'string') {
    text += obj + ' ';
  } else if (typeof obj === 'number' || typeof obj === 'boolean') {
    text += obj.toString() + ' ';
  } else if (Array.isArray(obj)) {
    for (const item of obj) {
      text += extractTextFromJSON(item, depth + 1);
    }
  } else if (obj && typeof obj === 'object') {
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        // Include key names as context
        text += key + ': ';
        text += extractTextFromJSON(obj[key], depth + 1);
      }
    }
  }
  
  return text;
}
