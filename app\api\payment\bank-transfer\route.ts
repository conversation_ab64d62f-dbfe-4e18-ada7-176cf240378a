import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { isValidBankTransferReference } from '@/lib/trialManager';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { referenceCode, amount, plan } = await request.json();

    // Validate input
    if (!referenceCode || !amount || !plan) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    if (!isValidBankTransferReference(referenceCode)) {
      return NextResponse.json(
        { error: 'Invalid reference code format' },
        { status: 400 }
      );
    }

    // Validate plan and amount
    const validPlans = {
      monthly: 299000,
      yearly: 2990000
    };

    if (!validPlans[plan as keyof typeof validPlans] || validPlans[plan as keyof typeof validPlans] !== amount) {
      return NextResponse.json(
        { error: 'Invalid plan or amount' },
        { status: 400 }
      );
    }

    // In a real application, you would:
    // 1. Save the bank transfer record to the database
    // 2. Set status to 'pending'
    // 3. Send notification to admin for manual verification
    // 4. Update user's payment method preference

    console.log('Bank transfer payment submitted:', {
      userId: session.user.id,
      referenceCode,
      amount,
      plan,
      timestamp: new Date().toISOString()
    });

    // TODO: Insert into database
    // Example SQL:
    // INSERT INTO bank_transfers (
    //   user_id, reference_code, amount, currency, 
    //   verification_status, created_at
    // ) VALUES ($1, $2, $3, 'VND', 'pending', CURRENT_TIMESTAMP)

    // TODO: Update user's payment method preference
    // UPDATE users SET 
    //   payment_method_preference = 'bank_transfer',
    //   updated_at = CURRENT_TIMESTAMP
    // WHERE id = $1

    // TODO: Send notification to admin
    // You might want to send an email or webhook notification
    // to notify administrators about the pending bank transfer

    return NextResponse.json({
      success: true,
      referenceCode,
      status: 'pending',
      message: 'Bank transfer payment submitted successfully. We will verify your payment within 1-2 business days.',
      estimatedVerificationTime: '1-2 business days'
    });

  } catch (error) {
    console.error('Error processing bank transfer payment:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET endpoint to check payment status
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const referenceCode = searchParams.get('reference');

    if (!referenceCode) {
      return NextResponse.json(
        { error: 'Reference code required' },
        { status: 400 }
      );
    }

    // In a real application, you would query the database
    // SELECT * FROM bank_transfers 
    // WHERE user_id = $1 AND reference_code = $2

    console.log('Checking bank transfer status:', {
      userId: session.user.id,
      referenceCode
    });

    // Mock response - in real app, this would come from database
    return NextResponse.json({
      referenceCode,
      status: 'pending', // pending, verified, rejected
      submittedAt: new Date().toISOString(),
      verifiedAt: null,
      amount: 299000,
      currency: 'VND',
      notes: null
    });

  } catch (error) {
    console.error('Error checking bank transfer status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
