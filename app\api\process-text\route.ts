import { NextRequest, NextResponse } from 'next/server';
import { processTextDirectly } from '@/lib/textProcessor';
import { ProcessingOptions } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { text, options, language } = body;

    // Validate input
    if (!text || typeof text !== 'string') {
      return NextResponse.json({ error: 'Text is required' }, { status: 400 });
    }

    if (!options || typeof options !== 'object') {
      return NextResponse.json({ error: 'Processing options are required' }, { status: 400 });
    }

    // Validate text length (prevent abuse)
    if (text.length > 50000) {
      return NextResponse.json({ error: 'Text too long. Maximum 50,000 characters.' }, { status: 400 });
    }

    console.log('🚀 Server-side processing started');
    console.log('📝 Text length:', text.length);
    console.log('⚙️ Options:', options);
    console.log('🔑 API Key available:', !!process.env.HUGGINGFACE_API_KEY);

    // Process the text server-side where environment variables are available
    const result = await processTextDirectly(text, options as ProcessingOptions, language);

    console.log('✅ Processing completed successfully');
    console.log('📊 Result:', {
      originalLength: result.originalLength,
      newLength: result.newLength,
      improvementScore: result.improvementScore,
      detectionScore: result.detectionScore,
      processingTime: result.processingTime
    });

    return NextResponse.json({ success: true, result });

  } catch (error) {
    console.error('❌ Server-side processing failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Processing failed'
    }, { status: 500 });
  }
}
