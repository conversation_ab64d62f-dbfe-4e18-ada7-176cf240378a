import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import crypto from 'crypto';

/**
 * Referral System API
 * Handles referral tracking, credit awarding, and referral statistics
 */

interface ReferralData {
  referrerId: string;
  refereeId: string;
  referralCode: string;
  timestamp: string;
  creditsAwarded: {
    referrer: number;
    referee: number;
  };
  status: 'pending' | 'completed' | 'expired';
}

// Mock referral database - in production, use a real database
const mockReferrals: Map<string, ReferralData[]> = new Map();
const mockUserCredits: Map<string, number> = new Map();
const mockUserReferralCodes: Map<string, string> = new Map(); // userId -> referralCode
const mockReferralCodeToUser: Map<string, string> = new Map(); // referralCode -> userId

/**
 * Generate a unique referral code for a user
 * @param userId - The user ID to generate a code for
 * @returns A unique 8-character referral code
 */
function generateUniqueReferralCode(userId: string): string {
  // Check if user already has a referral code
  const existingCode = mockUserReferralCodes.get(userId);
  if (existingCode) {
    return existingCode;
  }

  let attempts = 0;
  const maxAttempts = 10;

  while (attempts < maxAttempts) {
    // Generate a unique code using user ID, timestamp, and random data
    const timestamp = Date.now().toString();
    const randomData = crypto.randomBytes(4).toString('hex');
    const combinedData = `${userId}-${timestamp}-${randomData}`;

    // Create hash and take first 8 characters
    const hash = crypto.createHash('sha256').update(combinedData).digest('hex');
    const code = hash.substring(0, 8).toUpperCase();
    const formattedCode = `GHOST-${code}`;

    // Check if this code already exists
    if (!mockReferralCodeToUser.has(formattedCode)) {
      // Store the mapping
      mockUserReferralCodes.set(userId, formattedCode);
      mockReferralCodeToUser.set(formattedCode, userId);
      return formattedCode;
    }

    attempts++;
  }

  // Fallback to timestamp-based code if all attempts fail
  const fallbackCode = `GHOST-${Date.now().toString().slice(-8)}`;
  mockUserReferralCodes.set(userId, fallbackCode);
  mockReferralCodeToUser.set(fallbackCode, userId);
  return fallbackCode;
}

/**
 * Get user ID from referral code
 * @param referralCode - The referral code to look up
 * @returns The user ID associated with the code, or null if not found
 */
function getUserIdFromReferralCode(referralCode: string): string | null {
  return mockReferralCodeToUser.get(referralCode) || null;
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    switch (action) {
      case 'stats':
        // Get user's referral statistics
        const userReferrals = mockReferrals.get(session.user.id) || [];
        const completedReferrals = userReferrals.filter(r => r.status === 'completed');
        const totalCreditsEarned = completedReferrals.reduce((sum, r) => sum + r.creditsAwarded.referrer, 0);

        // Generate or get user's unique referral code
        const userReferralCode = generateUniqueReferralCode(session.user.id);

        return NextResponse.json({
          success: true,
          data: {
            totalReferrals: completedReferrals.length,
            pendingReferrals: userReferrals.filter(r => r.status === 'pending').length,
            totalCreditsEarned,
            referralCode: userReferralCode,
            recentReferrals: completedReferrals.slice(-5).map(r => ({
              timestamp: r.timestamp,
              creditsEarned: r.creditsAwarded.referrer
            }))
          }
        });

      case 'leaderboard':
        // Get top referrers (mock data)
        const topReferrers = [
          { username: 'ReferralKing', referrals: 47, creditsEarned: 4700 },
          { username: 'ShareMaster', referrals: 32, creditsEarned: 3200 },
          { username: 'GrowthHacker', referrals: 28, creditsEarned: 2800 },
          { username: 'ViralUser', referrals: 23, creditsEarned: 2300 },
          { username: 'NetworkPro', referrals: 19, creditsEarned: 1900 }
        ];

        return NextResponse.json({
          success: true,
          data: {
            leaderboard: topReferrers,
            userRank: Math.floor(Math.random() * 100) + 1,
            userStats: {
              referrals: completedReferrals.length,
              creditsEarned: totalCreditsEarned
            }
          }
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action parameter' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Referral API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, referralCode, refereeId } = body;

    switch (action) {
      case 'track_referral':
        if (!referralCode) {
          return NextResponse.json(
            { error: 'Missing referral code' },
            { status: 400 }
          );
        }

        // Get referrer ID from referral code using the new system
        const referrerId = getUserIdFromReferralCode(referralCode);

        if (!referrerId) {
          return NextResponse.json(
            { error: 'Invalid referral code' },
            { status: 400 }
          );
        }

        // Prevent self-referral
        if (referrerId === session.user.id) {
          return NextResponse.json(
            { error: 'Cannot refer yourself' },
            { status: 400 }
          );
        }

        // Check if user was already referred
        const existingReferrals = Array.from(mockReferrals.values()).flat();
        const alreadyReferred = existingReferrals.some(r => r.refereeId === session.user.id);
        
        if (alreadyReferred) {
          return NextResponse.json(
            { error: 'User already referred' },
            { status: 400 }
          );
        }

        // Create referral record
        const referralData: ReferralData = {
          referrerId: referrerId,
          refereeId: session.user.id,
          referralCode,
          timestamp: new Date().toISOString(),
          creditsAwarded: {
            referrer: 100,
            referee: 50
          },
          status: 'completed'
        };

        // Store referral
        const referrerReferrals = mockReferrals.get(referrerId) || [];
        referrerReferrals.push(referralData);
        mockReferrals.set(referrerId, referrerReferrals);

        // Award credits
        const referrerCurrentCredits = mockUserCredits.get(referrerId) || 0;
        const refereeCurrentCredits = mockUserCredits.get(session.user.id) || 0;
        
        mockUserCredits.set(referrerId, referrerCurrentCredits + 100);
        mockUserCredits.set(session.user.id, refereeCurrentCredits + 50);

        return NextResponse.json({
          success: true,
          data: {
            referralTracked: true,
            creditsAwarded: {
              referee: 50,
              referrer: 100
            },
            message: 'Referral successful! You earned 50 bonus credits.'
          }
        });

      case 'generate_code':
        // Generate or get user's unique referral code
        const userReferralCode = generateUniqueReferralCode(session.user.id);

        return NextResponse.json({
          success: true,
          data: {
            referralCode: userReferralCode,
            referralLink: `${process.env.NEXTAUTH_URL}/ref/${userReferralCode}`,
            message: 'Referral code generated successfully'
          }
        });

      case 'validate_code':
        if (!referralCode) {
          return NextResponse.json(
            { error: 'Missing referral code' },
            { status: 400 }
          );
        }

        // Validate referral code format (now supports 8-character codes)
        const isValidFormat = /^GHOST-[A-Z0-9]{8}$/.test(referralCode);

        if (!isValidFormat) {
          return NextResponse.json({
            success: false,
            data: {
              valid: false,
              message: 'Invalid referral code format'
            }
          });
        }

        // Check if referrer exists using the new system
        const referrerUserId = getUserIdFromReferralCode(referralCode);
        const referrerExists = referrerUserId !== null;

        return NextResponse.json({
          success: true,
          data: {
            valid: referrerExists,
            message: referrerExists ? 'Valid referral code' : 'Referrer not found',
            bonusCredits: referrerExists ? 50 : 0
          }
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Referral POST API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
