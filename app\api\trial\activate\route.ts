import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { generateTrialActivation, isTrialEligible, UserTrialData } from '@/lib/trialManager';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Create user data object for trial eligibility check
    const userData: UserTrialData = {
      id: session.user.id,
      email: session.user.email,
      tier: session.user.tier,
      trialStartedAt: session.user.trialStartedAt,
      trialExpiresAt: session.user.trialExpiresAt,
      trialUsed: session.user.trialUsed,
      paymentMethodPreference: session.user.paymentMethodPreference,
      bankTransferVerified: session.user.bankTransferVerified
    };

    // Check if user is eligible for trial
    if (!isTrialEligible(userData)) {
      return NextResponse.json(
        { error: 'User is not eligible for trial' },
        { status: 400 }
      );
    }

    // Generate trial activation dates
    const { startDate, expiryDate } = generateTrialActivation();

    // In a real application, you would update the database here
    // For now, we'll simulate the database update
    console.log('Trial activation for user:', session.user.id, {
      startDate: startDate.toISOString(),
      expiryDate: expiryDate.toISOString()
    });

    // TODO: Update database with trial information
    // Example SQL:
    // UPDATE users SET 
    //   trial_started_at = $1,
    //   trial_expires_at = $2,
    //   trial_used = true,
    //   updated_at = CURRENT_TIMESTAMP
    // WHERE id = $3

    return NextResponse.json({
      success: true,
      trialStartedAt: startDate.toISOString(),
      trialExpiresAt: expiryDate.toISOString(),
      message: 'Trial activated successfully'
    });

  } catch (error) {
    console.error('Error activating trial:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
