'use client';

import { useState, lazy, Suspense } from 'react';
import { ProcessingOptions as ProcessingOptionsType } from '@/types';
import Header from '@/components/Header';
import TextInput from '@/components/TextInput';
import ProcessingOptions from '@/components/ProcessingOptions';
import OutputDisplay from '@/components/OutputDisplay';
import DetectionScore from '@/components/DetectionScore';
import Testimonials from '@/components/Testimonials';
import FAQ from '@/components/FAQ';
import AuthoritativeContent from '@/components/AuthoritativeContent';
import Footer from '@/components/Footer';
import LanguageSelector from '@/components/LanguageSelector';
import CommunityShowcase from '@/components/CommunityShowcase';
import LoadingSpinner from '@/components/LoadingSpinner';
import { ProcessingResult } from '@/types';
import { analytics } from '@/lib/analytics';
import { toast } from 'sonner';

// Lazy load heavy components to prevent build hanging
const ProductivityFeatures = lazy(() => import('@/components/ProductivityFeatures'));
const AIContentSuggestions = lazy(() => import('@/components/AIContentSuggestions'));

export default function Home() {
  const [inputText, setInputText] = useState('');
  const [result, setResult] = useState<ProcessingResult | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState('en');
  const [processingOptions, setProcessingOptions] = useState({
    intensity: 'medium',
    style: 'academic',
    preserveFormat: true,
    addVariations: false
  });

  const handleProcess = async () => {
    if (!inputText.trim()) return;

    setIsProcessing(true);
    try {
      // Add a small delay to show processing state
      await new Promise(resolve => setTimeout(resolve, 1000));

      const options: ProcessingOptionsType = {
        intensity: processingOptions.intensity as ProcessingOptionsType['intensity'],
        style: processingOptions.style as ProcessingOptionsType['style'],
        preserveFormat: processingOptions.preserveFormat,
        addVariations: processingOptions.addVariations
      };

      console.log('🚀 Starting text processing with options:', options);

      const startTime = Date.now();

      // Call server-side API for processing (where environment variables are available)
      const response = await fetch('/api/process-text', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: inputText,
          options,
          language: selectedLanguage
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Processing failed');
      }

      const { result: data } = await response.json();
      const processingTime = Date.now() - startTime;

      console.log('✅ Processing completed:', data);

      setResult(data);

      // Track analytics
      analytics.trackTextProcessed({
        style: options.style,
        intensity: options.intensity,
        textLength: inputText.length,
        processingTime: processingTime,
        hasVariations: options.addVariations
      });

    } catch (error) {
      console.error('Processing failed:', error);

      // Track error with additional context
      analytics.trackError('processing_failed', (error as Error).message);

      // Provide user-friendly error message
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      const userMessage = errorMessage.includes('network') || errorMessage.includes('fetch')
        ? 'Network error: Please check your internet connection and try again.'
        : errorMessage.includes('timeout')
        ? 'Processing timeout: The text might be too long. Try processing smaller sections.'
        : `Processing error: ${errorMessage}`;

      alert(userMessage);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleBatchComplete = (combinedText: string) => {
    setInputText(combinedText);
    toast.success('Batch processing completed! Text has been loaded into the main editor for further refinement.');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        <Header />

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mt-8">
          <div className="lg:col-span-2 space-y-6">
            <TextInput
              value={inputText}
              onChange={setInputText}
              onProcess={handleProcess}
              isProcessing={isProcessing}
            />

            <OutputDisplay
              originalText={inputText}
              result={result}
              isProcessing={isProcessing}
            />
          </div>

          <div className="space-y-6">
            <LanguageSelector
              selectedLanguage={selectedLanguage}
              onLanguageChange={setSelectedLanguage}
              inputText={inputText}
            />

            <ProcessingOptions
              options={processingOptions}
              onChange={setProcessingOptions}
            />

            <DetectionScore result={result} />

            {/* Advanced AI Features removed as requested */}

            <Suspense fallback={<LoadingSpinner message="Loading productivity features..." />}>
              <ProductivityFeatures
                onFeatureUse={(feature, data) => {
                  analytics.trackShare(`productivity_feature_${feature}`);
                  console.log('Productivity feature used:', feature, data);
                }}
                onBatchComplete={handleBatchComplete}
              />
            </Suspense>

            {inputText && (
              <Suspense fallback={<LoadingSpinner message="Loading AI suggestions..." />}>
                <AIContentSuggestions
                  text={inputText}
                  result={result}
                  onApplySuggestion={(suggestion) => {
                    analytics.trackShare('ai_suggestion_applied');
                    console.log('AI suggestion applied:', suggestion);
                  }}
                  onTextUpdate={(newText) => {
                    setInputText(newText);
                  }}
                />
              </Suspense>
            )}

            {/* Referral system is now triggered from ProcessingOptions component */}
          </div>
        </div>

        {/* Community Showcase Section */}
        <section id="community" className="mt-16">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-white mb-4">Community Showcase</h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              Join thousands of users sharing their amazing text transformations.
              Compete, learn, and get inspired by the community.
            </p>
            <div className="mt-4 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg max-w-3xl mx-auto">
              <h4 className="text-blue-300 font-semibold mb-2">🌟 What is Community Showcase?</h4>
              <p className="text-gray-300 text-sm leading-relaxed">
                The Community Showcase is a platform where GhostLayer users share their best text transformations,
                participate in weekly challenges, and learn from each other's techniques. Features include:
              </p>
              <ul className="text-gray-300 text-sm mt-2 space-y-1">
                <li>• <strong>Before/After Comparisons:</strong> See real examples of AI text humanization</li>
                <li>• <strong>Weekly Challenges:</strong> Compete in themed transformation contests</li>
                <li>• <strong>Community Voting:</strong> Rate and discover the most impressive transformations</li>
                <li>• <strong>Learning Hub:</strong> Tips, techniques, and best practices from expert users</li>
                <li>• <strong>Achievement System:</strong> Earn badges and recognition for quality contributions</li>
              </ul>
            </div>
          </div>
          <CommunityShowcase />
        </section>

        {/* How It Works / Features Section */}
        <section id="features" className="mt-16">
          <AuthoritativeContent />
        </section>

        {/* Testimonials Section */}
        <section id="testimonials" className="mt-16">
          <Testimonials />
        </section>

        {/* FAQ Section */}
        <section id="faq" className="mt-16">
          <FAQ />
        </section>
      </div>

      {/* Footer */}
      <Footer />
    </div>
  );
}