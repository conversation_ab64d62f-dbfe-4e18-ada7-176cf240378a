'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Crown,
  Check,
  X,
  Zap,
  Shield,
  Users,
  FileText,
  Clock,
  Star,
  ArrowRight,
  Sparkles
} from 'lucide-react';
import { toast } from 'sonner';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import TrialStatus from '@/components/TrialStatus';
import TrialActivationButton from '@/components/TrialActivationButton';
import PaymentMethodSelector from '@/components/PaymentMethodSelector';
import BankTransferPayment from '@/components/BankTransferPayment';
import { getTrialStatus, UserTrialData } from '@/lib/trialManager';

export default function PremiumPage() {
  const { data: session } = useSession();
  const [loading, setLoading] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<'monthly' | 'yearly'>('monthly');
  const [paymentMethod, setPaymentMethod] = useState<'lemonsqueezy' | 'bank_transfer'>('lemonsqueezy');
  const [showPaymentFlow, setShowPaymentFlow] = useState(false);

  // Debug logging
  console.log('Premium page render - showPaymentFlow:', showPaymentFlow);
  console.log('Premium page render - session:', session);

  // Get trial status if user is logged in
  const trialStatus = session?.user ? getTrialStatus({
    id: session.user.id,
    email: session.user.email,
    tier: session.user.tier,
    trialStartedAt: session.user.trialStartedAt,
    trialExpiresAt: session.user.trialExpiresAt,
    trialUsed: session.user.trialUsed,
    paymentMethodPreference: session.user.paymentMethodPreference,
    bankTransferVerified: session.user.bankTransferVerified
  }) : null;

  const handleUpgrade = async () => {
    console.log('handleUpgrade called');
    console.log('Session:', session);
    console.log('User:', session?.user);

    if (!session?.user) {
      console.log('No session or user, showing error toast');
      toast.error('Please sign in to upgrade to premium');
      return;
    }

    console.log('Setting showPaymentFlow to true');
    // Show payment method selection instead of direct checkout
    setShowPaymentFlow(true);
    console.log('showPaymentFlow set to true');
  };

  const handleLemonSqueezyCheckout = async () => {
    setLoading(true);

    try {
      // Get variant IDs from environment variables
      const monthlyVariantId = process.env.NEXT_PUBLIC_LEMONSQUEEZY_PRO_VARIANT_ID;
      const yearlyVariantId = process.env.NEXT_PUBLIC_LEMONSQUEEZY_YEARLY_VARIANT_ID;

      console.log('Environment variant IDs:', { monthlyVariantId, yearlyVariantId });

      const variantId = selectedPlan === 'monthly'
        ? parseInt(monthlyVariantId || '123457')
        : parseInt(yearlyVariantId || '123458');

      console.log('Selected plan:', selectedPlan, 'Variant ID:', variantId);

      const response = await fetch('/api/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          variantId,
          embed: true
        }),
      });

      console.log('Checkout API response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Checkout API error:', errorData);
        throw new Error(errorData.error || 'Failed to create checkout');
      }

      const responseData = await response.json();
      console.log('Checkout API response data:', responseData);

      const { checkoutUrl } = responseData;

      if (!checkoutUrl) {
        throw new Error('No checkout URL received from API');
      }

      console.log('Opening checkout URL:', checkoutUrl);

      // Open LemonSqueezy checkout
      if (typeof window !== 'undefined' && window.LemonSqueezy) {
        console.log('Using LemonSqueezy SDK to open checkout');
        window.LemonSqueezy.Url.Open(checkoutUrl);
      } else {
        console.log('Fallback: redirecting to checkout URL');
        // Fallback to redirect
        window.location.href = checkoutUrl;
      }
    } catch (error) {
      console.error('Upgrade error:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to start upgrade process');
    } finally {
      setLoading(false);
    }
  };

  // Load LemonSqueezy script
  useEffect(() => {
    const script = document.createElement('script');
    script.src = 'https://app.lemonsqueezy.com/js/lemon.js';
    script.defer = true;
    document.head.appendChild(script);

    script.onload = () => {
      if (typeof window.createLemonSqueezy === 'function') {
        window.createLemonSqueezy();
      }
    };

    return () => {
      if (document.head.contains(script)) {
        document.head.removeChild(script);
      }
    };
  }, []);

  const features = [
    {
      name: 'Text Processing',
      free: '1,000 words/day',
      premium: 'Unlimited words',
      icon: FileText
    },
    {
      name: 'Humanization Intensity',
      free: 'Light & Medium only',
      premium: 'All levels including Heavy',
      icon: Zap
    },
    {
      name: 'Batch Processing',
      free: '3 files max',
      premium: 'Unlimited files',
      icon: Users
    },
    {
      name: 'Processing Speed',
      free: 'Standard queue',
      premium: 'Priority processing',
      icon: Clock
    },
    {
      name: 'File Size Limit',
      free: '10MB per file',
      premium: '50MB per file',
      icon: Shield
    },
    {
      name: 'Export Formats',
      free: 'Text only',
      premium: 'Multiple formats',
      icon: Star
    }
  ];

  const premiumBenefits = [
    'Unlimited text processing with no daily limits',
    'Access to Heavy humanization intensity for maximum transformation',
    'Unlimited batch processing for multiple files',
    'Priority processing queue for faster results',
    'Larger file size limits up to 50MB per file',
    'Multiple export formats including PDF, DOCX, and more',
    'Advanced AI detection bypass algorithms',
    'Premium customer support with priority response',
    'Early access to new features and improvements',
    'Commercial usage rights for business applications'
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        <Header />
        
        {/* Hero Section */}
        <div className="text-center mb-16">
          <div className="flex justify-center mb-6">
            <div className="p-4 bg-gradient-to-br from-purple-500 to-blue-500 rounded-full">
              <Crown className="w-12 h-12 text-white" />
            </div>
          </div>
          
          <h1 className="text-5xl font-bold text-white mb-4 bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
            Upgrade to Premium
          </h1>
          
          <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
            Unlock the full potential of GhostLayer with unlimited processing, advanced features, and priority support
          </p>
          
          <div className="flex justify-center gap-4 mb-8">
            <Badge className="bg-purple-500/20 text-purple-300 px-4 py-2">
              <Sparkles className="w-4 h-4 mr-2" />
              Most Popular
            </Badge>
            <Badge className="bg-green-500/20 text-green-300 px-4 py-2">
              <Shield className="w-4 h-4 mr-2" />
              30-Day Money Back
            </Badge>
          </div>
        </div>

        {/* Trial Status Section */}
        {session?.user && (
          <div className="max-w-4xl mx-auto mb-12">
            {trialStatus?.isEligible && (
              <TrialActivationButton
                variant="card"
                className="mb-6"
                onTrialActivated={() => {
                  toast.success('Free trial activated! Enjoy 30 days of premium features.');
                }}
              />
            )}

            {(trialStatus?.isActive || session.user.tier === 'premium') && (
              <TrialStatus className="mb-6" />
            )}
          </div>
        )}

        {/* Payment Flow */}
        {showPaymentFlow && session?.user && (
          <div className="max-w-4xl mx-auto mb-12">
            <Card className="bg-white/5 backdrop-blur-lg border-white/10">
              <CardHeader>
                <CardTitle className="text-2xl font-bold text-white text-center">
                  Choose Your Payment Method
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* Plan Selection */}
                  <div className="flex justify-center gap-4 mb-6">
                    <button
                      onClick={() => setSelectedPlan('monthly')}
                      className={`px-6 py-3 rounded-lg font-medium transition-all ${
                        selectedPlan === 'monthly'
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                      }`}
                    >
                      Monthly - $19/month
                    </button>
                    <button
                      onClick={() => setSelectedPlan('yearly')}
                      className={`px-6 py-3 rounded-lg font-medium transition-all ${
                        selectedPlan === 'yearly'
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                      }`}
                    >
                      Yearly - $190/year (Save 17%)
                    </button>
                  </div>

                  {/* Payment Method Selection */}
                  <PaymentMethodSelector
                    selectedMethod={paymentMethod}
                    onMethodSelect={setPaymentMethod}
                  />

                  {/* Payment Processing */}
                  {paymentMethod === 'lemonsqueezy' ? (
                    <div className="text-center">
                      <Button
                        onClick={handleLemonSqueezyCheckout}
                        disabled={loading}
                        className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold px-8 py-3"
                      >
                        {loading ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            Processing...
                          </>
                        ) : (
                          'Continue to Checkout'
                        )}
                      </Button>
                    </div>
                  ) : (
                    <BankTransferPayment
                      plan={selectedPlan}
                      onPaymentInitiated={(referenceCode) => {
                        toast.success(`Bank transfer initiated with reference: ${referenceCode}`);
                      }}
                    />
                  )}

                  <div className="text-center">
                    <button
                      onClick={() => setShowPaymentFlow(false)}
                      className="text-gray-400 hover:text-white transition-colors"
                    >
                      ← Back to plans
                    </button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Pricing Cards */}
        {!showPaymentFlow && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16 max-w-4xl mx-auto">
          {/* Free Plan */}
          <Card className="bg-white/5 backdrop-blur-lg border-white/10">
            <CardHeader className="text-center pb-8">
              <CardTitle className="text-2xl font-bold text-white mb-2">Free Plan</CardTitle>
              <div className="text-4xl font-bold text-gray-300 mb-4">
                $0<span className="text-lg font-normal">/month</span>
              </div>
              <p className="text-gray-400">Perfect for trying out GhostLayer</p>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3 mb-8">
                {features.map((feature, index) => (
                  <li key={index} className="flex items-center gap-3">
                    <feature.icon className="w-5 h-5 text-gray-400" />
                    <span className="text-gray-300">{feature.free}</span>
                  </li>
                ))}
              </ul>
              <Button 
                disabled 
                className="w-full bg-gray-600 text-white cursor-not-allowed"
              >
                Current Plan
              </Button>
            </CardContent>
          </Card>

          {/* Premium Plan */}
          <Card className="bg-gradient-to-br from-purple-600/20 to-blue-600/20 border-purple-500/50 relative">
            <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
              <Badge className="bg-gradient-to-r from-purple-500 to-blue-500 text-white px-4 py-2">
                <Crown className="w-4 h-4 mr-2" />
                Recommended
              </Badge>
            </div>
            
            <CardHeader className="text-center pb-8 pt-8">
              <CardTitle className="text-2xl font-bold text-black mb-2">Premium Plan</CardTitle>
              <div className="text-4xl font-bold text-black mb-4">
                $19<span className="text-lg font-normal">/month</span>
              </div>
              <p className="text-black">Everything you need for professional use</p>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3 mb-8">
                {features.map((feature, index) => (
                  <li key={index} className="flex items-center gap-3">
                    <feature.icon className="w-5 h-5 text-purple-400" />
                    <span className="text-black font-medium">{feature.premium}</span>
                  </li>
                ))}
              </ul>
              <Button 
                onClick={handleUpgrade}
                disabled={loading}
                className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Processing...
                  </>
                ) : (
                  <>
                    Upgrade Now
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </div>
        )}

        {/* Feature Comparison Table */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-white text-center mb-8">
            Detailed Feature Comparison
          </h2>
          
          <Card className="bg-white/5 backdrop-blur-lg border-white/10 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-white/10">
                    <th className="text-left p-6 text-white font-semibold">Feature</th>
                    <th className="text-center p-6 text-white font-semibold">Free</th>
                    <th className="text-center p-6 text-white font-semibold bg-purple-500/10">Premium</th>
                  </tr>
                </thead>
                <tbody>
                  {features.map((feature, index) => (
                    <tr key={index} className="border-b border-white/5">
                      <td className="p-6 text-gray-300 font-medium">{feature.name}</td>
                      <td className="p-6 text-center text-gray-400">{feature.free}</td>
                      <td className="p-6 text-center text-white bg-purple-500/5 font-medium">{feature.premium}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </Card>
        </div>

        {/* Premium Benefits */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-white text-center mb-8">
            Why Choose Premium?
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
            {premiumBenefits.map((benefit, index) => (
              <div key={index} className="flex items-start gap-3">
                <div className="flex-shrink-0 w-6 h-6 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mt-0.5">
                  <Check className="w-4 h-4 text-white" />
                </div>
                <p className="text-gray-300">{benefit}</p>
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center mb-16">
          <Card className="bg-gradient-to-br from-purple-600/20 to-blue-600/20 border-purple-500/30 max-w-2xl mx-auto">
            <CardContent className="p-8">
              <h3 className="text-2xl font-bold text-black mb-4">
                Ready to Transform Your Content?
              </h3>
              <p className="text-black mb-6">
                Join thousands of professionals who trust GhostLayer for their content humanization needs
              </p>
              <Button 
                onClick={handleUpgrade}
                disabled={loading}
                size="lg"
                className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold px-8 py-3"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    Processing...
                  </>
                ) : (
                  <>
                    Start Your Premium Journey
                    <Crown className="w-5 h-5 ml-2" />
                  </>
                )}
              </Button>
              <p className="text-sm text-black mt-4">
                30-day money-back guarantee • Cancel anytime • Secure payment
              </p>
            </CardContent>
          </Card>
        </div>

        <Footer />
      </div>
    </div>
  );
}

// Extend window type for LemonSqueezy
declare global {
  interface Window {
    LemonSqueezy?: {
      Url: {
        Open: (url: string) => void;
      };
    };
    createLemonSqueezy?: () => void;
  }
}
