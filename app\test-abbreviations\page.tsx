'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { processTextDirectly } from '@/lib/textProcessor';
import { ProcessingOptions } from '@/types';

/**
 * Test page for abbreviation preservation in humanization
 * This page helps verify that abbreviations are properly maintained
 */
export default function TestAbbreviationsPage() {
  const [inputText, setInputText] = useState(
    "The AI system uses API calls to process data efficiently. Our CEO announced that the CTO will lead the new IT initiative. The HTML and CSS files need to be optimized for better UI/UX."
  );
  const [result, setResult] = useState<any>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const testCases = [
    {
      name: "Tech Abbreviations",
      text: "The AI system uses API calls to process data efficiently.",
      expectedAbbrevs: ["AI", "API"]
    },
    {
      name: "Business Roles",
      text: "Our CEO announced that the CTO will lead the new IT initiative.",
      expectedAbbrevs: ["CEO", "CTO", "IT"]
    },
    {
      name: "Web Development",
      text: "The HTML and CSS files need to be optimized for better UI/UX.",
      expectedAbbrevs: ["HTML", "CSS", "UI", "UX"]
    },
    {
      name: "Mixed Context",
      text: "NASA's research on AI and machine learning helps improve GPS accuracy.",
      expectedAbbrevs: ["NASA", "AI", "GPS"]
    },
    {
      name: "Financial Terms",
      text: "The 401k plan and IRA accounts offer tax advantages for employees.",
      expectedAbbrevs: ["401k", "IRA"]
    },
    {
      name: "Special Characters",
      text: "Our R&D team developed an SDK for iOS and Android apps.",
      expectedAbbrevs: ["R&D", "SDK", "iOS"]
    }
  ];

  const processText = async (text: string) => {
    setIsProcessing(true);
    try {
      const options: ProcessingOptions = {
        intensity: 'medium',
        style: 'balanced',
        language: 'en'
      };

      const result = await processTextDirectly(text, options);
      setResult(result);
    } catch (error) {
      console.error('Processing error:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const runTestCase = (testCase: any) => {
    setInputText(testCase.text);
    processText(testCase.text);
  };

  const checkAbbreviationPreservation = (originalText: string, processedText: string, expectedAbbrevs: string[]) => {
    const preserved = expectedAbbrevs.filter(abbrev => {
      const regex = new RegExp(`\\b${abbrev}\\b`, 'g');
      return regex.test(processedText);
    });

    const missing = expectedAbbrevs.filter(abbrev => {
      const regex = new RegExp(`\\b${abbrev}\\b`, 'g');
      return !regex.test(processedText);
    });

    return { preserved, missing };
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-6">
      <div className="max-w-6xl mx-auto space-y-6">
        <Card className="bg-white/5 backdrop-blur-lg border-white/10">
          <CardHeader>
            <CardTitle className="text-white text-2xl">
              🧪 Abbreviation Preservation Test
            </CardTitle>
            <p className="text-gray-300">
              Test the humanization algorithm's ability to preserve abbreviations like AI, API, CEO, etc.
            </p>
          </CardHeader>
        </Card>

        {/* Test Cases */}
        <Card className="bg-white/5 backdrop-blur-lg border-white/10">
          <CardHeader>
            <CardTitle className="text-white">Quick Test Cases</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {testCases.map((testCase, index) => (
                <div key={index} className="p-4 bg-gray-800/50 rounded-lg">
                  <h4 className="text-white font-medium mb-2">{testCase.name}</h4>
                  <p className="text-gray-300 text-sm mb-3">{testCase.text}</p>
                  <div className="flex flex-wrap gap-1 mb-3">
                    {testCase.expectedAbbrevs.map(abbrev => (
                      <Badge key={abbrev} variant="outline" className="text-xs">
                        {abbrev}
                      </Badge>
                    ))}
                  </div>
                  <Button
                    onClick={() => runTestCase(testCase)}
                    size="sm"
                    className="w-full bg-blue-600 hover:bg-blue-700"
                    disabled={isProcessing}
                  >
                    Test
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Custom Input */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="bg-white/5 backdrop-blur-lg border-white/10">
            <CardHeader>
              <CardTitle className="text-white">Input Text</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Textarea
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                placeholder="Enter text with abbreviations to test..."
                className="min-h-32 bg-gray-800/50 border-gray-600 text-white"
              />
              <Button
                onClick={() => processText(inputText)}
                className="w-full bg-green-600 hover:bg-green-700"
                disabled={isProcessing || !inputText.trim()}
              >
                {isProcessing ? 'Processing...' : 'Humanize Text'}
              </Button>
            </CardContent>
          </Card>

          <Card className="bg-white/5 backdrop-blur-lg border-white/10">
            <CardHeader>
              <CardTitle className="text-white">Results</CardTitle>
            </CardHeader>
            <CardContent>
              {result ? (
                <div className="space-y-4">
                  <div>
                    <h4 className="text-white font-medium mb-2">Humanized Text:</h4>
                    <div className="p-3 bg-gray-800/50 rounded text-gray-300">
                      {result.humanizedText}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h5 className="text-white font-medium mb-1">Improvement Score</h5>
                      <Badge variant="default" className="bg-green-600">
                        {result.improvementScore}%
                      </Badge>
                    </div>
                    <div>
                      <h5 className="text-white font-medium mb-1">Processing Time</h5>
                      <Badge variant="outline">
                        {result.processingTime}ms
                      </Badge>
                    </div>
                  </div>

                  {/* Abbreviation Analysis */}
                  <div>
                    <h4 className="text-white font-medium mb-2">Abbreviation Analysis:</h4>
                    <div className="space-y-2">
                      {(() => {
                        // Extract potential abbreviations from input
                        const inputAbbrevs = inputText.match(/\b[A-Z]{2,}\b/g) || [];
                        const uniqueInputAbbrevs = Array.from(new Set(inputAbbrevs));
                        
                        if (uniqueInputAbbrevs.length === 0) {
                          return <p className="text-gray-400">No abbreviations detected in input</p>;
                        }

                        const { preserved, missing } = checkAbbreviationPreservation(
                          inputText, 
                          result.humanizedText, 
                          uniqueInputAbbrevs
                        );

                        return (
                          <div className="space-y-2">
                            {preserved.length > 0 && (
                              <div>
                                <span className="text-green-400 font-medium">✅ Preserved: </span>
                                {preserved.map(abbrev => (
                                  <Badge key={abbrev} className="ml-1 bg-green-600">
                                    {abbrev}
                                  </Badge>
                                ))}
                              </div>
                            )}
                            {missing.length > 0 && (
                              <div>
                                <span className="text-red-400 font-medium">❌ Missing: </span>
                                {missing.map(abbrev => (
                                  <Badge key={abbrev} className="ml-1 bg-red-600">
                                    {abbrev}
                                  </Badge>
                                ))}
                              </div>
                            )}
                            {missing.length === 0 && preserved.length > 0 && (
                              <div className="text-green-400 font-medium">
                                🎉 All abbreviations preserved successfully!
                              </div>
                            )}
                          </div>
                        );
                      })()}
                    </div>
                  </div>
                </div>
              ) : (
                <p className="text-gray-400">Process some text to see results...</p>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
