'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Copy, Check, Building2, Clock, AlertCircle, CheckCircle } from 'lucide-react';
import { generateBankTransferReference } from '@/lib/trialManager';

interface BankTransferPaymentProps {
  plan: 'monthly' | 'yearly';
  onPaymentInitiated?: (referenceCode: string) => void;
  className?: string;
}

interface BankTransferInfo {
  referenceCode: string;
  amount: number;
  bankDetails: {
    bankName: string;
    accountNumber: string;
    accountHolder: string;
    swiftCode: string;
    branch: string;
  };
  instructions: {
    vi: string;
    en: string;
  };
}

export default function BankTransferPayment({ 
  plan, 
  onPaymentInitiated,
  className = '' 
}: BankTransferPaymentProps) {
  const { data: session } = useSession();
  const [transferInfo, setTransferInfo] = useState<BankTransferInfo | null>(null);
  const [copiedField, setCopiedField] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [paymentStatus, setPaymentStatus] = useState<'pending' | 'submitted' | 'verified' | 'rejected'>('pending');

  const pricing = {
    monthly: { amount: 299000, label: 'Monthly Plan', duration: '1 month' },
    yearly: { amount: 2990000, label: 'Yearly Plan', duration: '12 months' }
  };

  const currentPlan = pricing[plan];

  useEffect(() => {
    generateTransferInfo();
  }, [plan]);

  const generateTransferInfo = async () => {
    setIsGenerating(true);
    
    try {
      const referenceCode = generateBankTransferReference();
      
      const info: BankTransferInfo = {
        referenceCode,
        amount: currentPlan.amount,
        bankDetails: {
          bankName: 'Vietcombank',
          accountNumber: '**********',
          accountHolder: 'GHOSTLAYER VIETNAM',
          swiftCode: 'BFTVVNVX',
          branch: 'Ho Chi Minh City Branch'
        },
        instructions: {
          vi: `Chuyển khoản với nội dung: GHOSTLAYER-${referenceCode}`,
          en: `Transfer with content: GHOSTLAYER-${referenceCode}`
        }
      };

      setTransferInfo(info);
      onPaymentInitiated?.(referenceCode);

      // In a real app, you would save this to the database
      console.log('Generated bank transfer:', info);

    } catch (error) {
      console.error('Error generating transfer info:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const copyToClipboard = async (text: string, field: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedField(field);
      setTimeout(() => setCopiedField(''), 2000);
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount);
  };

  const handleSubmitPayment = async () => {
    if (!transferInfo) return;

    try {
      const response = await fetch('/api/payment/bank-transfer', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          referenceCode: transferInfo.referenceCode,
          amount: transferInfo.amount,
          plan: plan
        }),
      });

      if (response.ok) {
        setPaymentStatus('submitted');
      }
    } catch (error) {
      console.error('Error submitting payment:', error);
    }
  };

  if (isGenerating) {
    return (
      <div className={`bg-gray-800/50 border border-gray-700 rounded-lg p-6 ${className}`}>
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <span className="ml-3 text-gray-400">Generating transfer details...</span>
        </div>
      </div>
    );
  }

  if (!transferInfo) {
    return (
      <div className={`bg-red-500/10 border border-red-500/20 rounded-lg p-6 ${className}`}>
        <div className="flex items-center gap-3">
          <AlertCircle className="w-6 h-6 text-red-400" />
          <div>
            <h3 className="font-semibold text-red-400">Error</h3>
            <p className="text-red-300 text-sm">Failed to generate transfer details. Please try again.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Payment Summary */}
      <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-6">
        <div className="flex items-center gap-3 mb-4">
          <Building2 className="w-6 h-6 text-blue-400" />
          <h3 className="text-lg font-semibold text-white">Vietnamese Bank Transfer</h3>
        </div>
        
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-400">Plan:</span>
            <p className="text-white font-medium">{currentPlan.label}</p>
          </div>
          <div>
            <span className="text-gray-400">Amount:</span>
            <p className="text-white font-medium">{formatCurrency(currentPlan.amount)}</p>
          </div>
          <div>
            <span className="text-gray-400">Duration:</span>
            <p className="text-white font-medium">{currentPlan.duration}</p>
          </div>
          <div>
            <span className="text-gray-400">Reference:</span>
            <p className="text-white font-medium font-mono">{transferInfo.referenceCode}</p>
          </div>
        </div>
      </div>

      {/* Bank Details */}
      <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-6">
        <h4 className="font-semibold text-white mb-4">Bank Account Details</h4>
        
        <div className="space-y-4">
          {[
            { label: 'Bank Name', value: transferInfo.bankDetails.bankName, field: 'bankName' },
            { label: 'Account Number', value: transferInfo.bankDetails.accountNumber, field: 'accountNumber' },
            { label: 'Account Holder', value: transferInfo.bankDetails.accountHolder, field: 'accountHolder' },
            { label: 'SWIFT Code', value: transferInfo.bankDetails.swiftCode, field: 'swiftCode' },
            { label: 'Branch', value: transferInfo.bankDetails.branch, field: 'branch' }
          ].map(({ label, value, field }) => (
            <div key={field} className="flex items-center justify-between p-3 bg-gray-900/50 rounded-lg">
              <div>
                <span className="text-gray-400 text-sm">{label}</span>
                <p className="text-white font-medium">{value}</p>
              </div>
              <button
                onClick={() => copyToClipboard(value, field)}
                className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
              >
                {copiedField === field ? (
                  <Check className="w-4 h-4 text-green-400" />
                ) : (
                  <Copy className="w-4 h-4 text-gray-400" />
                )}
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Transfer Instructions */}
      <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-6">
        <h4 className="font-semibold text-yellow-400 mb-4">Transfer Instructions</h4>
        
        <div className="space-y-4">
          <div>
            <h5 className="text-white font-medium mb-2">Vietnamese (Tiếng Việt):</h5>
            <div className="flex items-center justify-between p-3 bg-gray-900/50 rounded-lg">
              <p className="text-gray-300 font-mono text-sm">{transferInfo.instructions.vi}</p>
              <button
                onClick={() => copyToClipboard(transferInfo.instructions.vi, 'instruction-vi')}
                className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
              >
                {copiedField === 'instruction-vi' ? (
                  <Check className="w-4 h-4 text-green-400" />
                ) : (
                  <Copy className="w-4 h-4 text-gray-400" />
                )}
              </button>
            </div>
          </div>
          
          <div>
            <h5 className="text-white font-medium mb-2">English:</h5>
            <div className="flex items-center justify-between p-3 bg-gray-900/50 rounded-lg">
              <p className="text-gray-300 font-mono text-sm">{transferInfo.instructions.en}</p>
              <button
                onClick={() => copyToClipboard(transferInfo.instructions.en, 'instruction-en')}
                className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
              >
                {copiedField === 'instruction-en' ? (
                  <Check className="w-4 h-4 text-green-400" />
                ) : (
                  <Copy className="w-4 h-4 text-gray-400" />
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Payment Status */}
      {paymentStatus === 'submitted' ? (
        <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-6">
          <div className="flex items-center gap-3">
            <CheckCircle className="w-6 h-6 text-green-400" />
            <div>
              <h4 className="font-semibold text-green-400">Payment Submitted</h4>
              <p className="text-green-300 text-sm">
                We'll verify your payment within 1-2 business days and activate your premium features.
              </p>
            </div>
          </div>
        </div>
      ) : (
        <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-6">
          <div className="flex items-center gap-3 mb-4">
            <Clock className="w-6 h-6 text-yellow-400" />
            <div>
              <h4 className="font-semibold text-white">Next Steps</h4>
              <p className="text-gray-400 text-sm">Complete your bank transfer and notify us</p>
            </div>
          </div>
          
          <ol className="list-decimal list-inside space-y-2 text-sm text-gray-300 mb-6">
            <li>Transfer the exact amount to the bank account above</li>
            <li>Use the provided reference code in the transfer description</li>
            <li>Click "I've Made the Transfer" button below</li>
            <li>We'll verify and activate your premium features within 1-2 business days</li>
          </ol>
          
          <button
            onClick={handleSubmitPayment}
            className="w-full bg-green-500 hover:bg-green-600 text-white font-medium py-3 px-6 rounded-lg transition-colors"
          >
            I've Made the Transfer
          </button>
        </div>
      )}
    </div>
  );
}
