'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  FileText, 
  Link, 
  CheckCircle, 
  AlertCircle, 
  RefreshCw, 
  Download,
  Upload,
  ExternalLink,
  Zap,
  Clock,
  X
} from 'lucide-react';
import { analytics } from '@/lib/analytics';

interface GoogleDoc {
  id: string;
  title: string;
  lastModified: string;
  url: string;
}

interface ProcessingResult {
  documentId: string;
  originalLength: number;
  humanizedLength: number;
  improvementScore: number;
  message: string;
  url: string;
  humanizedText?: string;
  originalText?: string;
}

export default function GoogleDocsIntegration() {
  const { data: session } = useSession();
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [documents, setDocuments] = useState<GoogleDoc[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedDoc, setSelectedDoc] = useState<GoogleDoc | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingResult, setProcessingResult] = useState<ProcessingResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    checkConnectionStatus();
  }, []);

  const checkConnectionStatus = async () => {
    try {
      const response = await fetch('/api/integrations/google-docs?action=status');

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        setIsConnected(data.data.connected);
        if (data.data.connected) {
          loadDocuments();
        } else if (data.data.error) {
          setError(data.data.error);
        }
      } else {
        setError(data.error || 'Failed to check connection status');
      }
    } catch (error) {
      console.error('Error checking connection status:', error);
      setError(`Connection check failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const connectGoogleDocs = async () => {
    try {
      setIsConnecting(true);
      setError(null);
      
      const response = await fetch('/api/integrations/google-docs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'connect' }),
      });

      const data = await response.json();
      
      if (data.success && data.data.authUrl) {
        // Open OAuth flow in new window
        window.open(data.data.authUrl, 'google-docs-auth', 'width=600,height=600');
        
        // Listen for auth completion
        const checkAuth = setInterval(() => {
          checkConnectionStatus();
          if (isConnected) {
            clearInterval(checkAuth);
            setIsConnecting(false);
          }
        }, 2000);
        
        // Stop checking after 5 minutes
        setTimeout(() => {
          clearInterval(checkAuth);
          setIsConnecting(false);
        }, 300000);
        
        analytics.trackShare('google_docs_connect_initiated');
      } else {
        setError(data.error || 'Failed to initiate Google Docs connection');
      }
    } catch (error) {
      console.error('Error connecting to Google Docs:', error);
      setError('Failed to connect to Google Docs');
    } finally {
      setIsConnecting(false);
    }
  };

  const loadDocuments = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/integrations/google-docs?action=documents');
      const data = await response.json();

      if (data.success) {
        setDocuments(data.data.documents);
        if (data.data.note) {
          setError(data.data.note);
        }
      } else {
        if (data.needsAuth) {
          setIsConnected(false);
          setError('Please connect your Google account to access your documents.');
        } else {
          setError(data.error || 'Failed to load documents');
        }
      }
    } catch (error) {
      console.error('Error loading documents:', error);
      setError('Failed to load documents');
    } finally {
      setIsLoading(false);
    }
  };

  const humanizeDocument = async (doc: GoogleDoc) => {
    try {
      setIsProcessing(true);
      setError(null);
      setSelectedDoc(doc);
      
      const response = await fetch('/api/integrations/google-docs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'humanize_document',
          documentId: doc.id,
          content: '' // API will fetch content
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        setProcessingResult(data.data);
        analytics.trackShare('google_docs_humanize_success');
      } else {
        if (data.needsAuth) {
          setIsConnected(false);
          setError('Google Docs authentication expired. Please reconnect your Google account.');
        } else {
          setError(data.error || 'Failed to humanize document');
        }
      }
    } catch (error) {
      console.error('Error humanizing document:', error);
      setError('Failed to humanize document');
    } finally {
      setIsProcessing(false);
    }
  };

  const createNewDocument = async () => {
    if (!processingResult) return;
    
    try {
      const response = await fetch('/api/integrations/google-docs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'create_document',
          title: `${selectedDoc?.title || 'Document'} - Humanized`,
          humanizedContent: processingResult.humanizedText
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        // Open the new document
        window.open(data.data.url, '_blank');
        analytics.trackShare('google_docs_create_success');

        // Refresh document list
        loadDocuments();
      } else {
        if (data.needsAuth) {
          setIsConnected(false);
          setError('Google Docs authentication expired. Please reconnect your Google account.');
        } else {
          setError(data.error || 'Failed to create document');
        }
      }
    } catch (error) {
      console.error('Error creating document:', error);
      setError('Failed to create document');
    }
  };

  const disconnect = async () => {
    try {
      const response = await fetch('/api/integrations/google-docs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'disconnect' }),
      });

      if (response.ok) {
        setIsConnected(false);
        setDocuments([]);
        setProcessingResult(null);
        setSelectedDoc(null);
        analytics.trackShare('google_docs_disconnect');
      }
    } catch (error) {
      console.error('Error disconnecting:', error);
    }
  };

  if (!session) {
    return (
      <Card className="bg-white/5 backdrop-blur-lg border-white/10">
        <CardContent className="p-6 text-center">
          <AlertCircle className="w-12 h-12 text-yellow-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-white mb-2">Sign In Required</h3>
          <p className="text-gray-300">Please sign in to use Google Docs integration.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Connection Status */}
      <Card className="bg-white/5 backdrop-blur-lg border-white/10">
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-white">
            <FileText className="w-5 h-5 text-blue-400" />
            Google Docs Integration
            {isConnected ? (
              <Badge className="bg-green-600 text-white">
                <CheckCircle className="w-3 h-3 mr-1" />
                Connected
              </Badge>
            ) : (
              <Badge variant="outline" className="border-gray-600 text-gray-300">
                <AlertCircle className="w-3 h-3 mr-1" />
                Not Connected
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {!isConnected ? (
            <div className="text-center">
              <p className="text-gray-300 mb-4">
                Connect your Google account to access and humanize your Google Docs directly.
              </p>
              <Button
                onClick={connectGoogleDocs}
                disabled={isConnecting}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {isConnecting ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    Connecting...
                  </>
                ) : (
                  <>
                    <Link className="w-4 h-4 mr-2" />
                    Connect Google Docs
                  </>
                )}
              </Button>
            </div>
          ) : (
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5 text-green-400" />
                <span className="text-white">Connected to Google Docs</span>
              </div>
              <div className="flex gap-2">
                <Button
                  onClick={loadDocuments}
                  variant="outline"
                  size="sm"
                  className="border-gray-600 text-gray-300 hover:bg-gray-800"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Refresh
                </Button>
                <Button
                  onClick={disconnect}
                  variant="outline"
                  size="sm"
                  className="border-red-600 text-red-400 hover:bg-red-900/20"
                >
                  Disconnect
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Card className="bg-red-500/10 border-red-500/20">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <AlertCircle className="w-5 h-5 text-red-400" />
              <span className="text-red-300">{error}</span>
              <Button
                onClick={() => setError(null)}
                variant="ghost"
                size="sm"
                className="ml-auto text-red-400 hover:text-red-300"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Documents and Processing */}
      {isConnected && (
        <Tabs defaultValue="documents" className="w-full">
          <TabsList className="grid w-full grid-cols-2 bg-gray-800/50">
            <TabsTrigger value="documents" className="text-white">
              Your Documents
            </TabsTrigger>
            <TabsTrigger value="results" className="text-white">
              Processing Results
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="documents" className="space-y-4">
            <Card className="bg-white/5 backdrop-blur-lg border-white/10">
              <CardHeader>
                <CardTitle className="text-white">Available Documents</CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="text-center py-8">
                    <RefreshCw className="w-8 h-8 text-blue-400 animate-spin mx-auto mb-4" />
                    <p className="text-gray-300">Loading documents...</p>
                  </div>
                ) : documents.length === 0 ? (
                  <div className="text-center py-8">
                    <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-300">No documents found</p>
                    <Button
                      onClick={loadDocuments}
                      variant="outline"
                      className="mt-4 border-gray-600 text-gray-300 hover:bg-gray-800"
                    >
                      <RefreshCw className="w-4 h-4 mr-2" />
                      Refresh
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {documents.map((doc) => (
                      <div
                        key={doc.id}
                        className="flex items-center justify-between p-4 bg-gray-800/30 rounded-lg border border-gray-700"
                      >
                        <div className="flex-1">
                          <h4 className="text-white font-medium">{doc.title}</h4>
                          <p className="text-gray-400 text-sm">
                            Last modified: {new Date(doc.lastModified).toLocaleDateString()}
                          </p>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            onClick={() => window.open(doc.url, '_blank')}
                            variant="outline"
                            size="sm"
                            className="border-gray-600 text-gray-300 hover:bg-gray-800"
                          >
                            <ExternalLink className="w-4 h-4" />
                          </Button>
                          <Button
                            onClick={() => humanizeDocument(doc)}
                            disabled={isProcessing}
                            size="sm"
                            className="bg-purple-600 hover:bg-purple-700"
                          >
                            {isProcessing && selectedDoc?.id === doc.id ? (
                              <>
                                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                                Processing...
                              </>
                            ) : (
                              <>
                                <Zap className="w-4 h-4 mr-2" />
                                Humanize
                              </>
                            )}
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="results" className="space-y-4">
            <Card className="bg-white/5 backdrop-blur-lg border-white/10">
              <CardHeader>
                <CardTitle className="text-white">Processing Results</CardTitle>
              </CardHeader>
              <CardContent>
                {processingResult ? (
                  <div className="space-y-4">
                    <div className="grid grid-cols-3 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-400">
                          {processingResult.originalLength}
                        </div>
                        <div className="text-gray-400 text-sm">Original Length</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-400">
                          {processingResult.humanizedLength}
                        </div>
                        <div className="text-gray-400 text-sm">Humanized Length</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-400">
                          {processingResult.improvementScore}%
                        </div>
                        <div className="text-gray-400 text-sm">Improvement</div>
                      </div>
                    </div>
                    
                    <div className="flex gap-2">
                      <Button
                        onClick={createNewDocument}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <Upload className="w-4 h-4 mr-2" />
                        Create New Doc
                      </Button>
                      <Button
                        onClick={() => window.open(processingResult.url, '_blank')}
                        variant="outline"
                        className="border-gray-600 text-gray-300 hover:bg-gray-800"
                      >
                        <ExternalLink className="w-4 h-4 mr-2" />
                        View Original
                      </Button>
                    </div>
                    
                    <div className="p-4 bg-green-500/10 border border-green-500/20 rounded-lg">
                      <p className="text-green-300 text-sm">{processingResult.message}</p>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Clock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-300">No processing results yet</p>
                    <p className="text-gray-400 text-sm">
                      Select a document and click "Humanize" to see results here
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}
