'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { CreditCard, Building2, Globe, MapPin, Check } from 'lucide-react';

interface PaymentMethodSelectorProps {
  onMethodSelect: (method: 'lemonsqueezy' | 'bank_transfer') => void;
  selectedMethod?: 'lemonsqueezy' | 'bank_transfer';
  className?: string;
}

export default function PaymentMethodSelector({ 
  onMethodSelect, 
  selectedMethod,
  className = '' 
}: PaymentMethodSelectorProps) {
  const { data: session } = useSession();
  const [userCountry, setUserCountry] = useState<string>('');
  const [isDetectingLocation, setIsDetectingLocation] = useState(true);

  useEffect(() => {
    // Detect user's country/location
    const detectLocation = async () => {
      try {
        // Try to get location from browser API or IP geolocation service
        const response = await fetch('https://ipapi.co/json/');
        const data = await response.json();
        setUserCountry(data.country_code || '');
      } catch (error) {
        console.error('Error detecting location:', error);
        setUserCountry('');
      } finally {
        setIsDetectingLocation(false);
      }
    };

    detectLocation();
  }, []);

  const isVietnamese = userCountry === 'VN';

  const paymentMethods = [
    {
      id: 'lemonsqueezy' as const,
      name: 'International Payment',
      description: 'Credit card, PayPal, and other international methods',
      icon: <CreditCard className="w-6 h-6" />,
      badge: 'Recommended for international users',
      badgeColor: 'bg-blue-500/20 text-blue-400',
      features: [
        'Instant activation',
        'Automatic billing',
        'Multiple currencies',
        'Secure payment processing'
      ],
      recommended: !isVietnamese
    },
    {
      id: 'bank_transfer' as const,
      name: 'Vietnamese Bank Transfer',
      description: 'Direct bank transfer from any Vietnamese bank',
      icon: <Building2 className="w-6 h-6" />,
      badge: 'Recommended for Vietnamese users',
      badgeColor: 'bg-green-500/20 text-green-400',
      features: [
        'Support all Vietnamese banks',
        'No international fees',
        'Manual verification (1-2 business days)',
        'VND pricing'
      ],
      recommended: isVietnamese
    }
  ];

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center gap-2 text-sm text-gray-400 mb-6">
        <MapPin className="w-4 h-4" />
        {isDetectingLocation ? (
          'Detecting your location...'
        ) : userCountry ? (
          `Detected location: ${userCountry === 'VN' ? 'Vietnam' : 'International'}`
        ) : (
          'Location not detected'
        )}
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        {paymentMethods.map((method) => (
          <div
            key={method.id}
            className={`relative border rounded-lg p-6 cursor-pointer transition-all duration-200 hover:border-blue-500/50 ${
              selectedMethod === method.id
                ? 'border-blue-500 bg-blue-500/5'
                : 'border-gray-700 bg-gray-800/50'
            }`}
            onClick={() => onMethodSelect(method.id)}
          >
            {/* Recommended badge */}
            {method.recommended && (
              <div className={`absolute -top-2 left-4 px-2 py-1 rounded text-xs font-medium ${method.badgeColor}`}>
                {method.badge}
              </div>
            )}

            {/* Selection indicator */}
            {selectedMethod === method.id && (
              <div className="absolute top-4 right-4">
                <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                  <Check className="w-4 h-4 text-white" />
                </div>
              </div>
            )}

            <div className="flex items-start gap-4">
              <div className={`p-3 rounded-lg ${
                selectedMethod === method.id ? 'bg-blue-500/20' : 'bg-gray-700'
              }`}>
                {method.icon}
              </div>

              <div className="flex-1">
                <h3 className="text-lg font-semibold text-white mb-1">
                  {method.name}
                </h3>
                <p className="text-gray-400 text-sm mb-4">
                  {method.description}
                </p>

                <ul className="space-y-2">
                  {method.features.map((feature, index) => (
                    <li key={index} className="flex items-center gap-2 text-sm text-gray-300">
                      <div className="w-1.5 h-1.5 bg-gray-500 rounded-full" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Additional information */}
      <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-4 mt-6">
        <div className="flex items-start gap-3">
          <Globe className="w-5 h-5 text-blue-400 mt-0.5" />
          <div>
            <h4 className="font-medium text-white mb-2">Payment Security</h4>
            <p className="text-sm text-gray-400">
              Both payment methods are secure and encrypted. International payments are processed through LemonSqueezy, 
              while Vietnamese bank transfers are verified manually by our team within 1-2 business days.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
