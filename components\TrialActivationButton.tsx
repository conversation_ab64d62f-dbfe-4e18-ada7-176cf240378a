'use client';

import { useSession } from 'next-auth/react';
import { useState } from 'react';
import { Star, Loader2, CheckCircle } from 'lucide-react';
import { getTrialStatus, isTrialEligible, UserTrialData } from '@/lib/trialManager';

interface TrialActivationButtonProps {
  variant?: 'button' | 'card';
  className?: string;
  onTrialActivated?: () => void;
}

export default function TrialActivationButton({ 
  variant = 'button', 
  className = '',
  onTrialActivated 
}: TrialActivationButtonProps) {
  const { data: session, update } = useSession();
  const [isActivating, setIsActivating] = useState(false);
  const [isActivated, setIsActivated] = useState(false);

  if (!session?.user) return null;

  const userData: UserTrialData = {
    id: session.user.id,
    email: session.user.email,
    tier: session.user.tier,
    trialStartedAt: session.user.trialStartedAt,
    trialExpiresAt: session.user.trialExpiresAt,
    trialUsed: session.user.trialUsed,
    paymentMethodPreference: session.user.paymentMethodPreference,
    bankTransferVerified: session.user.bankTransferVerified
  };

  const trialStatus = getTrialStatus(userData);

  // Don't show if user is not eligible or already has active trial
  if (!trialStatus.isEligible || trialStatus.isActive || session.user.tier === 'premium') {
    return null;
  }

  const handleActivateTrial = async () => {
    setIsActivating(true);
    
    try {
      const response = await fetch('/api/trial/activate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to activate trial');
      }

      const result = await response.json();
      
      // Update the session with new trial information
      await update({
        ...session,
        user: {
          ...session.user,
          trialStartedAt: result.trialStartedAt,
          trialExpiresAt: result.trialExpiresAt,
          trialUsed: true
        }
      });

      setIsActivated(true);
      onTrialActivated?.();

      // Reset activation state after showing success
      setTimeout(() => {
        setIsActivated(false);
      }, 2000);

    } catch (error) {
      console.error('Error activating trial:', error);
      // You might want to show an error toast here
    } finally {
      setIsActivating(false);
    }
  };

  if (variant === 'card') {
    return (
      <div className={`bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-lg p-6 ${className}`}>
        <div className="flex items-start gap-4">
          <div className="p-2 bg-blue-500/20 rounded-lg">
            <Star className="w-6 h-6 text-blue-400" />
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-white mb-2">
              Start Your Free Trial
            </h3>
            <p className="text-gray-300 text-sm mb-4">
              Get 30 days of premium features including Heavy transformation mode and batch processing.
            </p>
            <ul className="text-sm text-gray-400 space-y-1 mb-6">
              <li className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-400" />
                Heavy transformation intensity
              </li>
              <li className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-400" />
                Batch file processing
              </li>
              <li className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-400" />
                Priority support
              </li>
              <li className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-400" />
                Advanced analytics
              </li>
            </ul>
            <button
              onClick={handleActivateTrial}
              disabled={isActivating || isActivated}
              className="w-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 disabled:opacity-50 disabled:cursor-not-allowed text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 flex items-center justify-center gap-2"
            >
              {isActivating ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Activating Trial...
                </>
              ) : isActivated ? (
                <>
                  <CheckCircle className="w-4 h-4" />
                  Trial Activated!
                </>
              ) : (
                <>
                  <Star className="w-4 h-4" />
                  Start Free Trial
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <button
      onClick={handleActivateTrial}
      disabled={isActivating || isActivated}
      className={`bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 disabled:opacity-50 disabled:cursor-not-allowed text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 flex items-center gap-2 ${className}`}
    >
      {isActivating ? (
        <>
          <Loader2 className="w-4 h-4 animate-spin" />
          Activating...
        </>
      ) : isActivated ? (
        <>
          <CheckCircle className="w-4 h-4" />
          Activated!
        </>
      ) : (
        <>
          <Star className="w-4 h-4" />
          Start Free Trial
        </>
      )}
    </button>
  );
}
