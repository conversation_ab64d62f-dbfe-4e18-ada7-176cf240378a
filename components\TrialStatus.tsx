'use client';

import { useSession } from 'next-auth/react';
import { useState } from 'react';
import { Clock, Star, AlertTriangle, CheckCircle } from 'lucide-react';
import { getTrialStatus, formatRemainingTime, UserTrialData } from '@/lib/trialManager';

interface TrialStatusProps {
  className?: string;
  showDetails?: boolean;
}

export default function TrialStatus({ className = '', showDetails = true }: TrialStatusProps) {
  const { data: session } = useSession();
  const [isExpanded, setIsExpanded] = useState(false);

  if (!session?.user) return null;

  const userData: UserTrialData = {
    id: session.user.id,
    email: session.user.email,
    tier: session.user.tier,
    trialStartedAt: session.user.trialStartedAt,
    trialExpiresAt: session.user.trialExpiresAt,
    trialUsed: session.user.trialUsed,
    paymentMethodPreference: session.user.paymentMethodPreference,
    bankTransferVerified: session.user.bankTransferVerified
  };

  const trialStatus = getTrialStatus(userData);

  // Don't show if user is premium or has no trial activity
  if (session.user.tier === 'premium' || (!trialStatus.isActive && !trialStatus.isEligible)) {
    return null;
  }

  const getStatusIcon = () => {
    if (trialStatus.isActive) {
      if (trialStatus.daysRemaining <= 3) {
        return <AlertTriangle className="w-4 h-4 text-yellow-400" />;
      }
      return <CheckCircle className="w-4 h-4 text-green-400" />;
    }
    return <Star className="w-4 h-4 text-blue-400" />;
  };

  const getStatusColor = () => {
    if (trialStatus.isActive) {
      if (trialStatus.daysRemaining <= 3) {
        return 'bg-yellow-500/10 border-yellow-500/20 text-yellow-400';
      }
      return 'bg-green-500/10 border-green-500/20 text-green-400';
    }
    return 'bg-blue-500/10 border-blue-500/20 text-blue-400';
  };

  const getStatusText = () => {
    if (trialStatus.isActive) {
      const timeText = formatRemainingTime(trialStatus.timeRemaining);
      return `Trial expires in ${timeText}`;
    }
    if (trialStatus.isEligible) {
      return 'Start your free 30-day trial';
    }
    return 'Trial expired';
  };

  return (
    <div className={`rounded-lg border ${getStatusColor()} p-4 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          {getStatusIcon()}
          <div>
            <p className="font-medium text-sm">{getStatusText()}</p>
            {trialStatus.isActive && showDetails && (
              <p className="text-xs opacity-75 mt-1">
                Premium features included
              </p>
            )}
          </div>
        </div>
        
        {showDetails && (
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-xs opacity-75 hover:opacity-100 transition-opacity"
          >
            {isExpanded ? 'Less' : 'More'}
          </button>
        )}
      </div>

      {isExpanded && showDetails && (
        <div className="mt-4 pt-4 border-t border-current/10">
          <div className="space-y-2 text-xs">
            {trialStatus.isActive && (
              <>
                <div className="flex justify-between">
                  <span className="opacity-75">Started:</span>
                  <span>{trialStatus.startDate?.toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="opacity-75">Expires:</span>
                  <span>{trialStatus.expiryDate?.toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="opacity-75">Days remaining:</span>
                  <span className="font-medium">{trialStatus.daysRemaining}</span>
                </div>
              </>
            )}
            
            {trialStatus.isEligible && (
              <div className="space-y-1">
                <p className="opacity-75">Trial includes:</p>
                <ul className="list-disc list-inside space-y-1 ml-2">
                  <li>Heavy transformation mode</li>
                  <li>Batch processing</li>
                  <li>Priority support</li>
                  <li>Advanced analytics</li>
                </ul>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
