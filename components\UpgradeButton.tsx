'use client';

import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Crown, Zap, ArrowRight, Sparkles } from 'lucide-react';

interface UpgradeButtonProps {
  variant?: 'button' | 'card';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export default function UpgradeButton({
  variant = 'button',
  size = 'md',
  className = ''
}: UpgradeButtonProps) {
  const router = useRouter();

  const handleUpgrade = () => {
    // Navigate to premium page instead of direct checkout
    router.push('/premium');
  };

  if (variant === 'card') {
    return (
      <Card className={`bg-gradient-to-br from-purple-600/20 to-blue-600/20 border-purple-500/30 ${className}`}>
        <CardContent className="p-6">
          <div className="text-center space-y-4">
            <div className="flex justify-center">
              <div className="p-3 bg-gradient-to-br from-purple-500 to-blue-500 rounded-full">
                <Crown className="w-6 h-6 text-white" />
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold text-white mb-2">
                Upgrade to Premium
              </h3>
              <p className="text-sm text-gray-300 mb-4">
                Unlock unlimited processing, advanced features, and priority support
              </p>
            </div>

            <div className="space-y-2 text-xs text-gray-400">
              <div className="flex items-center justify-center gap-2">
                <Zap className="w-3 h-3" />
                <span>100,000 words/month</span>
              </div>
              <div className="flex items-center justify-center gap-2">
                <Sparkles className="w-3 h-3" />
                <span>Advanced humanization</span>
              </div>
              <div className="flex items-center justify-center gap-2">
                <ArrowRight className="w-3 h-3" />
                <span>Priority processing</span>
              </div>
            </div>

            <Button
              onClick={handleUpgrade}
              disabled={loading}
              className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Processing...
                </>
              ) : (
                <>
                  <Crown className="w-4 h-4 mr-2" />
                  Upgrade Now - $29/month
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Button variant
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2',
    lg: 'px-6 py-3 text-lg'
  };

  return (
    <Button
      onClick={handleUpgrade}
      className={`bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold ${sizeClasses[size]} ${className}`}
    >
      <Crown className="w-4 h-4 mr-2" />
      Upgrade to Premium
    </Button>
  );
}
