-- Migration: Add Trial System Fields
-- Run this script to add trial system support to existing databases

-- Add trial system fields to users table
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS trial_started_at TIMESTAMP,
ADD COLUMN IF NOT EXISTS trial_expires_at TIMESTAMP,
ADD COLUMN IF NOT EXISTS trial_used BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS payment_method_preference VARCHAR(20) DEFAULT 'lemonsqueezy',
ADD COLUMN IF NOT EXISTS bank_transfer_verified BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS bank_transfer_reference VARCHAR(50);

-- Add constraint for payment method preference
ALTER TABLE users 
ADD CONSTRAINT IF NOT EXISTS check_payment_method 
CHECK (payment_method_preference IN ('lemonsqueezy', 'bank_transfer'));

-- Create index for trial queries
CREATE INDEX IF NOT EXISTS idx_users_trial_expires ON users(trial_expires_at);
CREATE INDEX IF NOT EXISTS idx_users_trial_used ON users(trial_used);

-- Create bank transfers table for tracking manual payments
CREATE TABLE IF NOT EXISTS bank_transfers (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  reference_code VARCHAR(50) UNIQUE NOT NULL,
  amount INTEGER NOT NULL, -- in cents
  currency VARCHAR(3) DEFAULT 'VND',
  bank_name VARCHAR(100),
  account_holder VARCHAR(255),
  transfer_date TIMESTAMP,
  verification_status VARCHAR(20) DEFAULT 'pending' CHECK (verification_status IN ('pending', 'verified', 'rejected')),
  verified_by INTEGER REFERENCES users(id),
  verified_at TIMESTAMP,
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create index for bank transfer queries
CREATE INDEX IF NOT EXISTS idx_bank_transfers_reference ON bank_transfers(reference_code);
CREATE INDEX IF NOT EXISTS idx_bank_transfers_user ON bank_transfers(user_id);
CREATE INDEX IF NOT EXISTS idx_bank_transfers_status ON bank_transfers(verification_status);

-- Insert Vietnamese bank account information (for admin reference)
CREATE TABLE IF NOT EXISTS payment_methods (
  id SERIAL PRIMARY KEY,
  method_type VARCHAR(20) NOT NULL,
  country_code VARCHAR(2),
  is_active BOOLEAN DEFAULT TRUE,
  config JSONB,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert Vietnamese bank transfer configuration
INSERT INTO payment_methods (method_type, country_code, is_active, config) VALUES
('bank_transfer', 'VN', TRUE, '{
  "bank_name": "Vietcombank",
  "account_number": "**********",
  "account_holder": "GHOSTLAYER VIETNAM",
  "swift_code": "BFTVVNVX",
  "branch": "Ho Chi Minh City Branch",
  "instructions": {
    "vi": "Chuyển khoản với nội dung: GHOSTLAYER-[MÃ THAM CHIẾU]",
    "en": "Transfer with content: GHOSTLAYER-[REFERENCE CODE]"
  },
  "pricing": {
    "monthly": 299000,
    "yearly": 2990000
  }
}')
ON CONFLICT DO NOTHING;

-- Insert LemonSqueezy configuration
INSERT INTO payment_methods (method_type, country_code, is_active, config) VALUES
('lemonsqueezy', NULL, TRUE, '{
  "store_id": "your-store-id",
  "variants": {
    "monthly": 123456,
    "yearly": 123457
  }
}')
ON CONFLICT DO NOTHING;
