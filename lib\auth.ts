import { NextAuthOptions } from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import CredentialsProvider from 'next-auth/providers/credentials';

export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID || '',
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
      authorization: {
        params: {
          prompt: "consent",
          access_type: "offline",
          response_type: "code"
        }
      }
    }),
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        // In a real app, you would validate against a database
        // For demo purposes, we'll use a simple check
        if (credentials.email === '<EMAIL>' && credentials.password === 'demo123') {
          return {
            id: '1',
            email: credentials.email,
            name: 'Demo User',
            image: null,
          };
        }

        return null;
      }
    })
  ],
  callbacks: {
    async jwt({ token, user, account }) {
      if (user) {
        token.id = user.id;
        // Add user tier information
        token.tier = 'free'; // Default to free tier
        token.credits = 100; // Default credits

        // Add trial information (would be fetched from database in production)
        token.trialStartedAt = null;
        token.trialExpiresAt = null;
        token.trialUsed = false;
        token.paymentMethodPreference = 'lemonsqueezy';
        token.bankTransferVerified = false;
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string;
        session.user.tier = token.tier as string;
        session.user.credits = token.credits as number;

        // Add trial information to session
        session.user.trialStartedAt = token.trialStartedAt as string | null;
        session.user.trialExpiresAt = token.trialExpiresAt as string | null;
        session.user.trialUsed = token.trialUsed as boolean;
        session.user.paymentMethodPreference = token.paymentMethodPreference as string;
        session.user.bankTransferVerified = token.bankTransferVerified as boolean;
      }
      return session;
    },
  },
  pages: {
    signIn: '/auth/signin',
    error: '/auth/error', // Error code passed in query string as ?error=
  },
  session: {
    strategy: 'jwt',
  },
  debug: process.env.NODE_ENV === 'development',
};

// Extend the built-in session types
declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
      tier: string;
      credits: number;
    };
  }

  interface User {
    tier?: string;
    credits?: number;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string;
    tier: string;
    credits: number;
  }
}
