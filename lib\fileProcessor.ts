// File processing utilities for batch operations
import { processTextDirectly } from './textProcessor';
import { ProcessingOptions, ProcessingResult } from '@/types';

// Helper function to extract text via server-side API
async function extractTextViaAPI(file: File): Promise<string> {
  const formData = new FormData();
  formData.append('file', file);

  const response = await fetch('/api/extract-text', {
    method: 'POST',
    body: formData,
  });

  // Handle response - read body only once
  let responseData;
  let responseText: string;

  try {
    responseText = await response.text();
  } catch (error) {
    throw new Error(`Failed to read response: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  if (!response.ok) {
    let errorMessage = 'Failed to extract text from file';

    // Try to parse as JSON first
    try {
      const errorData = JSON.parse(responseText);
      errorMessage = errorData.error || errorMessage;
    } catch (jsonError) {
      // If we can't parse as <PERSON><PERSON><PERSON>, check if it's HTML
      if (responseText.includes('<!DOCTYPE') || responseText.includes('<html>')) {
        errorMessage = 'Server returned HTML error page instead of JSON. This may indicate a server-side processing error.';
        console.error('HTML error response received:', responseText.substring(0, 200));
      } else {
        errorMessage = `Server error: ${response.status} ${response.statusText}`;
      }
    }
    throw new Error(errorMessage);
  }

  // Parse successful response
  try {
    responseData = JSON.parse(responseText);
  } catch (jsonError) {
    if (responseText.includes('<!DOCTYPE') || responseText.includes('<html>')) {
      throw new Error('Server returned HTML instead of JSON. This indicates a server-side error during file processing.');
    }
    throw new Error(`Invalid JSON response from server: ${jsonError instanceof Error ? jsonError.message : 'Unknown JSON parsing error'}`);
  }

  if (!responseData.text) {
    throw new Error('No text content received from server');
  }

  return responseData.text;
}

export interface FileProcessingResult {
  fileName: string;
  originalSize: number;
  processedSize: number;
  result: ProcessingResult;
  error?: string;
}

export interface BatchProcessingResult {
  results: FileProcessingResult[];
  totalFiles: number;
  successfulFiles: number;
  failedFiles: number;
  totalProcessingTime: number;
  averageImprovementScore: number;
}

// Supported file types
export const SUPPORTED_FILE_TYPES = {
  'text/plain': ['.txt'],
  'text/markdown': ['.md'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
  'application/pdf': ['.pdf'],
  'application/rtf': ['.rtf'],
  'application/vnd.oasis.opendocument.text': ['.odt']
};

// Extract text from different file formats
export async function extractTextFromFile(file: File): Promise<string> {
  const fileType = file.type;
  const fileName = file.name.toLowerCase();

  try {
    switch (fileType) {
      case 'text/plain':
      case 'text/markdown':
        return await readTextFile(file);
      
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        return await extractFromDocx(file);
      
      case 'application/pdf':
        return await extractFromPdf(file);
      
      case 'application/rtf':
        return await extractFromRtf(file);
      
      case 'application/vnd.oasis.opendocument.text':
        return await extractFromOdt(file);
      
      default:
        // Try to detect by file extension if MIME type is not recognized
        if (fileName.endsWith('.txt') || fileName.endsWith('.md')) {
          return await readTextFile(file);
        } else if (fileName.endsWith('.docx')) {
          return await extractFromDocx(file);
        } else if (fileName.endsWith('.pdf')) {
          return await extractFromPdf(file);
        } else if (fileName.endsWith('.rtf')) {
          return await extractFromRtf(file);
        } else if (fileName.endsWith('.odt')) {
          return await extractFromOdt(file);
        } else if (fileName.endsWith('.json')) {
          return await extractFromJson(file);
        } else if (fileName.endsWith('.csv')) {
          return await extractFromCsv(file);
        } else if (fileName.endsWith('.html') || fileName.endsWith('.htm')) {
          return await extractFromHtml(file);
        } else if (fileName.endsWith('.xml')) {
          return await extractFromXml(file);
        } else {
          // Try to read as plain text as last resort
          try {
            const content = await readTextFile(file);
            if (content && content.trim().length > 0) {
              return content;
            }
          } catch {
            // If plain text reading fails, throw the original error
          }
        }

        throw new Error(`Unsupported file type: ${fileType || 'unknown'}. Supported formats: .txt, .md, .docx, .pdf, .rtf, .odt, .json, .csv, .html, .xml`);
    }
  } catch (error) {
    console.error(`File processing error for ${file.name}:`, {
      fileName: file.name,
      fileType: file.type,
      fileSize: file.size,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });

    throw new Error(`Failed to extract text from ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Read plain text files
async function readTextFile(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      if (content) {
        resolve(content);
      } else {
        reject(new Error('Failed to read file content'));
      }
    };
    reader.onerror = () => reject(new Error('File reading failed'));
    reader.readAsText(file);
  });
}

// Extract text from DOCX files using server-side API
async function extractFromDocx(file: File): Promise<string> {
  return extractTextViaAPI(file);
}

// Extract text from PDF files using server-side API
async function extractFromPdf(file: File): Promise<string> {
  return extractTextViaAPI(file);
}

// Extract text from RTF files using server-side API
async function extractFromRtf(file: File): Promise<string> {
  return extractTextViaAPI(file);
}

// Extract text from ODT files using server-side API
async function extractFromOdt(file: File): Promise<string> {
  return extractTextViaAPI(file);
}

// Extract text from JSON files using server-side API
async function extractFromJson(file: File): Promise<string> {
  return extractTextViaAPI(file);
}

// Extract text from CSV files using server-side API
async function extractFromCsv(file: File): Promise<string> {
  return extractTextViaAPI(file);
}

// Extract text from HTML files using server-side API
async function extractFromHtml(file: File): Promise<string> {
  return extractTextViaAPI(file);
}

// Extract text from XML files using server-side API
async function extractFromXml(file: File): Promise<string> {
  return extractTextViaAPI(file);
}



// Process a single file
export async function processFile(
  file: File, 
  options: ProcessingOptions
): Promise<FileProcessingResult> {
  const startTime = Date.now();
  
  try {
    // Extract text from file
    const extractedText = await extractTextFromFile(file);
    
    if (!extractedText || extractedText.trim().length === 0) {
      throw new Error('No text content found in file');
    }
    
    // Process the extracted text
    const result = await processTextDirectly(extractedText, options);
    
    return {
      fileName: file.name,
      originalSize: file.size,
      processedSize: result.humanizedText.length,
      result: result
    };
  } catch (error) {
    return {
      fileName: file.name,
      originalSize: file.size,
      processedSize: 0,
      result: {
        humanizedText: '',
        improvementScore: 0,
        detectionScore: 0,
        confidence: 0,
        readabilityScore: 0,
        processingTime: Date.now() - startTime,
        originalLength: 0,
        newLength: 0,
        variations: []
      },
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Process multiple files in batch
export async function processBatchFiles(
  files: File[], 
  options: ProcessingOptions,
  onProgress?: (completed: number, total: number) => void
): Promise<BatchProcessingResult> {
  const startTime = Date.now();
  const results: FileProcessingResult[] = [];
  
  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    const result = await processFile(file, options);
    results.push(result);
    
    // Report progress
    if (onProgress) {
      onProgress(i + 1, files.length);
    }
    
    // Add small delay to prevent UI blocking
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  const successfulFiles = results.filter(r => !r.error).length;
  const failedFiles = results.length - successfulFiles;
  const totalProcessingTime = Date.now() - startTime;
  
  // Calculate average improvement score (only for successful files)
  const successfulResults = results.filter(r => !r.error);
  const averageImprovementScore = successfulResults.length > 0
    ? successfulResults.reduce((sum, r) => sum + r.result.improvementScore, 0) / successfulResults.length
    : 0;
  
  return {
    results,
    totalFiles: files.length,
    successfulFiles,
    failedFiles,
    totalProcessingTime,
    averageImprovementScore
  };
}

// Validate file before processing
export function validateFile(file: File): { valid: boolean; error?: string } {
  // Check file size (max 50MB)
  if (file.size > 50 * 1024 * 1024) {
    return { valid: false, error: 'File size exceeds 50MB limit' };
  }

  // Check if file type is supported
  const fileType = file.type;
  const fileName = file.name.toLowerCase();

  const supportedExtensions = ['.txt', '.md', '.docx', '.pdf', '.rtf', '.odt', '.json', '.csv', '.html', '.htm', '.xml'];
  const isSupportedType = Object.keys(SUPPORTED_FILE_TYPES).includes(fileType) ||
    supportedExtensions.some(ext => fileName.endsWith(ext));

  if (!isSupportedType) {
    return { valid: false, error: `Unsupported file type. Supported formats: ${supportedExtensions.join(', ')}` };
  }

  return { valid: true };
}

// Generate download content for batch results
export function generateBatchResultsFile(batchResult: BatchProcessingResult): string {
  const timestamp = new Date().toISOString();
  let content = `GhostLayer Batch Processing Results\n`;
  content += `Generated: ${timestamp}\n`;
  content += `Total Files: ${batchResult.totalFiles}\n`;
  content += `Successful: ${batchResult.successfulFiles}\n`;
  content += `Failed: ${batchResult.failedFiles}\n`;
  content += `Average Improvement: ${batchResult.averageImprovementScore.toFixed(1)}%\n`;
  content += `Total Processing Time: ${(batchResult.totalProcessingTime / 1000).toFixed(2)}s\n\n`;
  
  content += `${'='.repeat(80)}\n\n`;
  
  batchResult.results.forEach((result, index) => {
    content += `File ${index + 1}: ${result.fileName}\n`;
    content += `Status: ${result.error ? 'FAILED' : 'SUCCESS'}\n`;
    
    if (result.error) {
      content += `Error: ${result.error}\n`;
    } else {
      content += `Improvement Score: ${result.result.improvementScore}%\n`;
      content += `Processing Time: ${result.result.processingTime}ms\n`;
      content += `Original Size: ${result.originalSize} bytes\n`;
      content += `Processed Size: ${result.processedSize} bytes\n\n`;
      content += `HUMANIZED CONTENT:\n`;
      content += `${'-'.repeat(40)}\n`;
      content += `${result.result.humanizedText}\n`;
    }
    
    content += `\n${'='.repeat(80)}\n\n`;
  });
  
  return content;
}
