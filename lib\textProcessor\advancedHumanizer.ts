import { ProcessingOptions } from '@/types';

/**
 * Advanced Humanization Algorithm
 * Designed to achieve <30% AI detection rates while preserving meaning
 */

interface SentenceAnalysis {
  text: string;
  length: number;
  isPassive: boolean;
  hasAIPatterns: boolean;
  formalityScore: number;
  needsRestructuring: boolean;
}

interface HumanizationContext {
  totalSentences: number;
  averageLength: number;
  formalityLevel: number;
  aiPatternCount: number;
}

// Comprehensive AI-typical words to replace
const AI_TYPICAL_WORDS = {
  'furthermore': ['also', 'plus', 'and', 'what\'s more', 'on top of that'],
  'moreover': ['also', 'plus', 'and', 'besides', 'what\'s more'],
  'additionally': ['also', 'plus', 'and', 'on top of that', 'besides'],
  'consequently': ['so', 'therefore', 'as a result', 'because of this'],
  'nevertheless': ['but', 'however', 'still', 'even so', 'yet'],
  'comprehensive': ['complete', 'thorough', 'full', 'detailed', 'extensive'],
  'significant': ['important', 'major', 'big', 'substantial', 'considerable'],
  'substantial': ['large', 'big', 'significant', 'considerable', 'major'],
  'facilitate': ['help', 'enable', 'make easier', 'assist', 'support'],
  'utilize': ['use', 'employ', 'apply', 'make use of'],
  'implement': ['put in place', 'carry out', 'execute', 'apply', 'use'],
  'optimize': ['improve', 'enhance', 'make better', 'refine', 'perfect'],
  'enhance': ['improve', 'boost', 'strengthen', 'upgrade', 'better'],
  'various': ['different', 'many', 'several', 'multiple', 'diverse'],
  'numerous': ['many', 'lots of', 'plenty of', 'several', 'multiple'],
  'demonstrate': ['show', 'prove', 'display', 'reveal', 'illustrate'],
  'indicate': ['show', 'suggest', 'point to', 'reveal', 'signal'],
  'establish': ['set up', 'create', 'build', 'form', 'start'],
  'maintain': ['keep', 'preserve', 'sustain', 'continue', 'uphold'],
  'ensure': ['make sure', 'guarantee', 'see to it', 'confirm'],
  'crucial': ['important', 'vital', 'key', 'essential', 'critical'],
  'essential': ['important', 'vital', 'key', 'crucial', 'necessary'],
  'fundamental': ['basic', 'core', 'key', 'essential', 'main'],
  'paramount': ['most important', 'crucial', 'vital', 'key', 'top priority']
};

// Natural qualifiers and hedging words
const NATURAL_QUALIFIERS = [
  'quite', 'rather', 'somewhat', 'pretty', 'fairly', 'relatively',
  'generally', 'usually', 'often', 'typically', 'mostly', 'largely'
];

// Casual transition words
const CASUAL_TRANSITIONS = [
  'Plus', 'Also', 'And', 'But', 'So', 'Now', 'Well', 'Actually',
  'In fact', 'By the way', 'What\'s more', 'On top of that'
];

// Contractions mapping
const CONTRACTIONS = {
  'do not': 'don\'t',
  'does not': 'doesn\'t',
  'did not': 'didn\'t',
  'will not': 'won\'t',
  'would not': 'wouldn\'t',
  'could not': 'couldn\'t',
  'should not': 'shouldn\'t',
  'cannot': 'can\'t',
  'is not': 'isn\'t',
  'are not': 'aren\'t',
  'was not': 'wasn\'t',
  'were not': 'weren\'t',
  'have not': 'haven\'t',
  'has not': 'hasn\'t',
  'had not': 'hadn\'t',
  'it is': 'it\'s',
  'that is': 'that\'s',
  'there is': 'there\'s',
  'they are': 'they\'re',
  'we are': 'we\'re',
  'you are': 'you\'re',
  'I am': 'I\'m',
  'he is': 'he\'s',
  'she is': 'she\'s'
};

export function applyAdvancedHumanization(text: string, options: ProcessingOptions): string {
  if (options.intensity !== 'medium' && options.intensity !== 'heavy') {
    return text; // Only apply to medium and heavy modes
  }

  // Debug logging to confirm algorithm is running
  console.log(`🚀 Advanced humanization activated for ${options.intensity} mode`);

  // Phase 1: Analyze text structure
  const context = analyzeTextContext(text);
  const sentences = analyzeSentences(text);

  // Phase 2: Restructure sentences
  let restructuredSentences = restructureSentences(sentences, context);

  // Phase 3: Apply advanced synonym replacement
  restructuredSentences = applyAdvancedSynonymReplacement(restructuredSentences, context, options);

  // Phase 4: Apply humanization techniques
  restructuredSentences = applyHumanizationTechniques(restructuredSentences, context, options);

  // Phase 5: Final AI pattern removal
  const result = removeAIPatterns(restructuredSentences.join(' '), context);

  return result;
}

function analyzeTextContext(text: string): HumanizationContext {
  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
  const words = text.split(/\s+/);
  
  const totalLength = words.length;
  const averageLength = totalLength / sentences.length;
  
  // Count AI patterns
  let aiPatternCount = 0;
  const aiWords = Object.keys(AI_TYPICAL_WORDS);
  aiWords.forEach(word => {
    const regex = new RegExp(`\\b${word}\\b`, 'gi');
    const matches = text.match(regex);
    if (matches) aiPatternCount += matches.length;
  });

  // Calculate formality level
  const formalTransitions = text.match(/\b(furthermore|moreover|additionally|consequently|nevertheless)\b/gi) || [];
  const formalityLevel = formalTransitions.length / sentences.length;

  return {
    totalSentences: sentences.length,
    averageLength,
    formalityLevel,
    aiPatternCount
  };
}

function analyzeSentences(text: string): SentenceAnalysis[] {
  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
  
  return sentences.map(sentence => {
    const words = sentence.trim().split(/\s+/);
    const length = words.length;
    
    // Check for passive voice
    const isPassive = /\b(is|are|was|were|been|being)\s+\w+ed\b/.test(sentence);
    
    // Check for AI patterns
    const hasAIPatterns = Object.keys(AI_TYPICAL_WORDS).some(word => 
      new RegExp(`\\b${word}\\b`, 'i').test(sentence)
    );
    
    // Calculate formality score
    const formalWords = ['furthermore', 'moreover', 'consequently', 'nevertheless', 'comprehensive'];
    const formalCount = formalWords.filter(word => 
      new RegExp(`\\b${word}\\b`, 'i').test(sentence)
    ).length;
    const formalityScore = formalCount / words.length;
    
    // Determine if restructuring is needed
    const needsRestructuring = length > 25 || isPassive || hasAIPatterns || formalityScore > 0.1;
    
    return {
      text: sentence.trim(),
      length,
      isPassive,
      hasAIPatterns,
      formalityScore,
      needsRestructuring
    };
  });
}

function restructureSentences(sentences: SentenceAnalysis[], context: HumanizationContext): string[] {
  return sentences.map((sentence, index) => {
    let result = sentence.text;
    
    if (!sentence.needsRestructuring) {
      return result;
    }
    
    // Split overly long sentences
    if (sentence.length > 25) {
      result = splitLongSentence(result);
    }
    
    // Convert some passive voice to active
    if (sentence.isPassive && Math.random() < 0.7) {
      result = convertPassiveToActive(result);
    }
    
    // Vary sentence starters
    if (index > 0 && Math.random() < 0.3) {
      result = varysentenceStarter(result);
    }
    
    return result;
  });
}

function splitLongSentence(sentence: string): string {
  // Look for natural break points
  const breakPoints = [', and ', ', but ', ', so ', ', which ', ', that '];
  
  for (const breakPoint of breakPoints) {
    if (sentence.includes(breakPoint)) {
      const parts = sentence.split(breakPoint);
      if (parts.length === 2 && parts[0].split(' ').length > 10) {
        return parts[0] + '. ' + parts[1].charAt(0).toUpperCase() + parts[1].slice(1);
      }
    }
  }
  
  return sentence;
}

function convertPassiveToActive(sentence: string): string {
  // Simple passive to active conversion patterns
  const passivePatterns = [
    {
      pattern: /(\w+)\s+is\s+(\w+ed)\s+by\s+(\w+)/gi,
      replacement: '$3 $2 $1'
    },
    {
      pattern: /(\w+)\s+are\s+(\w+ed)\s+by\s+(\w+)/gi,
      replacement: '$3 $2 $1'
    },
    {
      pattern: /(\w+)\s+was\s+(\w+ed)\s+by\s+(\w+)/gi,
      replacement: '$3 $2 $1'
    },
    {
      pattern: /(\w+)\s+were\s+(\w+ed)\s+by\s+(\w+)/gi,
      replacement: '$3 $2 $1'
    }
  ];
  
  let result = sentence;
  passivePatterns.forEach(({ pattern, replacement }) => {
    result = result.replace(pattern, replacement);
  });
  
  return result;
}

function varysentenceStarter(sentence: string): string {
  const starters = ['Actually, ', 'In fact, ', 'Interestingly, ', 'Notably, ', 'Importantly, '];
  
  if (Math.random() < 0.5) {
    const starter = starters[Math.floor(Math.random() * starters.length)];
    return starter + sentence.toLowerCase();
  }
  
  return sentence;
}

function applyAdvancedSynonymReplacement(sentences: string[], context: HumanizationContext, options: ProcessingOptions): string[] {
  // Adjust replacement rate based on intensity
  const replacementRate = options.intensity === 'medium' ? 0.6 : 0.8; // 60% for medium, 80% for heavy
  
  return sentences.map(sentence => {
    let result = sentence;
    
    // Replace AI-typical words with high priority
    Object.entries(AI_TYPICAL_WORDS).forEach(([aiWord, alternatives]) => {
      const regex = new RegExp(`\\b${aiWord}\\b`, 'gi');
      if (regex.test(result) && Math.random() < replacementRate) {
        const replacement = alternatives[Math.floor(Math.random() * alternatives.length)];
        result = result.replace(regex, replacement);
      }
    });
    
    return result;
  });
}

function applyHumanizationTechniques(sentences: string[], context: HumanizationContext, options: ProcessingOptions): string[] {
  // Adjust humanization intensity based on mode
  const contractionRate = options.intensity === 'medium' ? 0.4 : 0.6; // 40% for medium, 60% for heavy
  const qualifierRate = options.intensity === 'medium' ? 0.2 : 0.4; // 20% for medium, 40% for heavy

  return sentences.map((sentence, index) => {
    let result = sentence;

    // Add contractions
    Object.entries(CONTRACTIONS).forEach(([formal, contraction]) => {
      const regex = new RegExp(`\\b${formal}\\b`, 'gi');
      if (Math.random() < contractionRate) {
        result = result.replace(regex, contraction);
      }
    });

    // Add natural qualifiers
    if (Math.random() < qualifierRate) {
      const qualifier = NATURAL_QUALIFIERS[Math.floor(Math.random() * NATURAL_QUALIFIERS.length)];
      // Insert qualifier before adjectives
      result = result.replace(/\b(important|significant|effective|useful)\b/gi, `${qualifier} $1`);
    }
    
    // Replace formal transitions with casual ones
    if (index > 0 && Math.random() < 0.5) {
      const formalTransitions = ['Furthermore', 'Moreover', 'Additionally', 'Consequently'];
      formalTransitions.forEach(formal => {
        if (result.startsWith(formal)) {
          const casual = CASUAL_TRANSITIONS[Math.floor(Math.random() * CASUAL_TRANSITIONS.length)];
          result = result.replace(formal, casual);
        }
      });
    }
    
    return result;
  });
}

function removeAIPatterns(text: string, context: HumanizationContext): string {
  let result = text;
  
  // Remove excessive formality
  result = result.replace(/\b(in conclusion|to summarize|in summary)\b/gi, 'Overall');
  result = result.replace(/\b(it is important to note that)\b/gi, 'Note that');
  result = result.replace(/\b(it should be noted that)\b/gi, 'Keep in mind that');
  
  // Add natural inconsistencies
  result = addNaturalInconsistencies(result);
  
  // Clean up spacing
  result = result.replace(/\s{2,}/g, ' ').trim();
  
  return result;
}

function addNaturalInconsistencies(text: string): string {
  let result = text;
  
  // Occasionally use em dashes instead of commas
  if (Math.random() < 0.3) {
    result = result.replace(/, which /g, ' — which ');
  }
  
  // Add occasional parenthetical remarks
  if (Math.random() < 0.2) {
    result = result.replace(/\. ([A-Z])/g, '. (And $1');
    result = result.replace(/\. \(And ([^.]+)\./g, '. (And $1.)');
  }
  
  return result;
}
