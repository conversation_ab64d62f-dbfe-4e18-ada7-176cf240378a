import { ProcessingOptions, ProcessingResult } from '@/types';

/**
 * Google Gemma-2-2B-IT Integration for Enhanced AI Text Humanization
 * Provides state-of-the-art text humanization using Google's instruction-tuned model
 */

interface GemmaConfig {
  modelEndpoint: string;
  maxTokens: number;
  temperature: number;
  topP: number;
  apiKey?: string;
  timeout: number;
}

interface GemmaRequest {
  instruction: string;
  text: string;
  preserveStructure: boolean;
  style: string;
  intensity: string;
}

interface GemmaResponse {
  humanizedText: string;
  confidence: number;
  processingTime: number;
  tokensUsed: number;
  success: boolean;
}

interface DocumentStructure {
  paragraphs: ParagraphInfo[];
  originalFormat: string;
  metadata: {
    totalSentences: number;
    averageWordsPerSentence: number;
    formalityScore: number;
  };
}

interface ParagraphInfo {
  content: string;
  isEmpty: boolean;
  index: number;
  sentences: SentenceInfo[];
}

interface SentenceInfo {
  text: string;
  punctuation: string;
  aiPatternScore: number;
  complexity: number;
  needsHumanization: boolean;
}

// Configuration for different deployment options
const GEMMA_CONFIGS = {
  huggingface: {
    modelEndpoint: 'https://api-inference.huggingface.co/models/google/gemma-2-2b-it',
    maxTokens: 1024,
    temperature: 0.7,
    topP: 0.9,
    timeout: 30000
  },
  vertexai: {
    modelEndpoint: 'https://us-central1-aiplatform.googleapis.com/v1/projects/PROJECT_ID/locations/us-central1/publishers/google/models/gemma-2b-it',
    maxTokens: 1024,
    temperature: 0.7,
    topP: 0.9,
    timeout: 30000
  },
  local: {
    modelEndpoint: 'http://localhost:8080/generate',
    maxTokens: 1024,
    temperature: 0.7,
    topP: 0.9,
    timeout: 15000
  }
};

// Instruction templates for different humanization scenarios
const HUMANIZATION_INSTRUCTIONS = {
  base: `Rewrite this text to sound more natural and human-like while preserving the exact meaning and structure.`,
  
  aiPatternRemoval: `Remove AI-typical language patterns such as:
- Overuse of words like "furthermore", "moreover", "comprehensive", "significant"
- Overly formal academic transitions
- Repetitive sentence structures
- Excessive use of passive voice
- Uniform sentence lengths`,
  
  naturalFlow: `Make the text flow more naturally by:
- Varying sentence lengths and structures
- Using more conversational transitions
- Adding natural contractions where appropriate
- Including subtle human-like inconsistencies
- Making it sound like a knowledgeable person wrote it`,
  
  styleSpecific: {
    academic: `Maintain academic rigor while reducing AI formality. Use natural academic language that a human scholar would write.`,
    professional: `Keep professional tone but make it sound like it was written by an experienced human professional, not an AI.`,
    casual: `Convert to natural, conversational language that sounds like a knowledgeable person explaining the topic to a friend.`,
    creative: `Add natural human creativity and expression while maintaining factual accuracy.`
  },
  
  structurePreservation: `CRITICAL: Maintain all paragraph breaks, punctuation, and formatting exactly as provided. Do not add or remove line breaks.`
};

export class GemmaHumanizer {
  private config: GemmaConfig;
  private cache = new Map<string, GemmaResponse>();
  private rateLimiter = new APIRateLimiter();

  constructor(provider: 'huggingface' | 'vertexai' | 'local' = 'huggingface') {
    this.config = GEMMA_CONFIGS[provider];

    // Set API key from environment
    if (provider === 'huggingface') {
      this.config.apiKey = process.env.HUGGINGFACE_API_KEY;
      console.log('🔑 Hugging Face API key:', this.config.apiKey ? 'Available' : 'Missing');
    } else if (provider === 'vertexai') {
      this.config.apiKey = process.env.GOOGLE_CLOUD_API_KEY;
      console.log('🔑 Google Cloud API key:', this.config.apiKey ? 'Available' : 'Missing');
    }

    if (!this.config.apiKey && provider !== 'local') {
      console.warn(`⚠️ No API key found for ${provider}. Gemma integration will not work.`);
    }
  }

  /**
   * Main entry point for Gemma-enhanced humanization
   */
  async humanizeText(text: string, options: ProcessingOptions): Promise<string> {
    try {
      // Check rate limits
      await this.rateLimiter.checkLimit();

      // Analyze document structure
      const structure = this.analyzeDocumentStructure(text);

      // Process based on intensity mode
      switch (options.intensity) {
        case 'medium':
          return await this.processMediumMode(structure, options);
        case 'heavy':
          return await this.processHeavyMode(structure, options);
        case 'ultra':
          return await this.processUltraMode(structure, options);
        default:
          return text; // Light mode uses custom algorithm only
      }
    } catch (error) {
      console.warn('Gemma processing failed:', error);
      throw error; // Let the hybrid processor handle fallback
    }
  }

  /**
   * Medium Mode: Selective Gemma processing for key sentences
   */
  private async processMediumMode(structure: DocumentStructure, options: ProcessingOptions): Promise<string> {
    const processedParagraphs = await Promise.all(
      structure.paragraphs.map(async (paragraph) => {
        if (paragraph.isEmpty) return paragraph;

        // Process sentences selectively
        const processedSentences = await Promise.all(
          paragraph.sentences.map(async (sentence, index) => {
            // Use Gemma for sentences with high AI patterns or every 3rd sentence
            if (sentence.aiPatternScore > 0.7 || index % 3 === 0) {
              const instruction = this.buildInstruction('selective', options, sentence);
              const response = await this.callGemmaAPI(instruction, sentence.text + sentence.punctuation);
              return response.success ? response.humanizedText : sentence.text + sentence.punctuation;
            } else {
              return sentence.text + sentence.punctuation;
            }
          })
        );

        return {
          ...paragraph,
          content: processedSentences.join(' ')
        };
      })
    );

    return this.reconstructDocument(processedParagraphs);
  }

  /**
   * Heavy Mode: Comprehensive Gemma processing with structure preservation
   */
  private async processHeavyMode(structure: DocumentStructure, options: ProcessingOptions): Promise<string> {
    const processedParagraphs = await Promise.all(
      structure.paragraphs.map(async (paragraph) => {
        if (paragraph.isEmpty) return paragraph;

        const instruction = this.buildInstruction('comprehensive', options);
        const response = await this.callGemmaAPI(instruction, paragraph.content);
        
        return {
          ...paragraph,
          content: response.success ? response.humanizedText : paragraph.content
        };
      })
    );

    return this.reconstructDocument(processedParagraphs);
  }

  /**
   * Ultra Mode: Gemma-primary processing with advanced instructions
   */
  private async processUltraMode(structure: DocumentStructure, options: ProcessingOptions): Promise<string> {
    // Process the entire document with advanced instructions
    const instruction = this.buildInstruction('ultra', options);
    const fullText = structure.paragraphs.map(p => p.content).join('\n\n');
    
    const response = await this.callGemmaAPI(instruction, fullText);
    
    if (response.success) {
      // Ensure structure is preserved
      return this.ensureStructurePreservation(response.humanizedText, structure);
    } else {
      // Fallback to heavy mode
      return await this.processHeavyMode(structure, options);
    }
  }

  /**
   * Build context-appropriate instructions for Gemma
   */
  private buildInstruction(mode: string, options: ProcessingOptions, sentence?: SentenceInfo): string {
    let instruction = HUMANIZATION_INSTRUCTIONS.base;

    // Add AI pattern removal instructions
    instruction += '\n\n' + HUMANIZATION_INSTRUCTIONS.aiPatternRemoval;

    // Add natural flow instructions
    instruction += '\n\n' + HUMANIZATION_INSTRUCTIONS.naturalFlow;

    // Add style-specific instructions
    const style = options.style || 'academic';
    if (HUMANIZATION_INSTRUCTIONS.styleSpecific[style as keyof typeof HUMANIZATION_INSTRUCTIONS.styleSpecific]) {
      instruction += '\n\n' + HUMANIZATION_INSTRUCTIONS.styleSpecific[style as keyof typeof HUMANIZATION_INSTRUCTIONS.styleSpecific];
    }

    // Add structure preservation (critical)
    instruction += '\n\n' + HUMANIZATION_INSTRUCTIONS.structurePreservation;

    // Mode-specific additions
    if (mode === 'selective' && sentence) {
      instruction += `\n\nFocus on making this sentence sound more natural while maintaining its role in the paragraph.`;
    } else if (mode === 'ultra') {
      instruction += `\n\nThis is premium processing - deliver the highest quality humanization possible while maintaining perfect structure and meaning.`;
    }

    instruction += '\n\nText to rewrite:';
    return instruction;
  }

  /**
   * Call Gemma API with proper error handling and caching
   */
  private async callGemmaAPI(instruction: string, text: string): Promise<GemmaResponse> {
    const cacheKey = this.getCacheKey(instruction, text);
    
    // Check cache first
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }

    const startTime = Date.now();
    
    try {
      const payload = {
        inputs: `${instruction}\n\n${text}`,
        parameters: {
          max_new_tokens: this.config.maxTokens,
          temperature: this.config.temperature,
          top_p: this.config.topP,
          do_sample: true,
          return_full_text: false
        }
      };

      const response = await fetch(this.config.modelEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(this.config.apiKey && { 'Authorization': `Bearer ${this.config.apiKey}` })
        },
        body: JSON.stringify(payload),
        signal: AbortSignal.timeout(this.config.timeout)
      });

      if (!response.ok) {
        throw new Error(`Gemma API error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      const processingTime = Date.now() - startTime;

      // Extract the humanized text from the response
      let humanizedText = '';
      if (Array.isArray(result) && result[0]?.generated_text) {
        humanizedText = result[0].generated_text.trim();
      } else if (result.generated_text) {
        humanizedText = result.generated_text.trim();
      } else {
        throw new Error('Invalid response format from Gemma API');
      }

      const gemmaResponse: GemmaResponse = {
        humanizedText,
        confidence: 0.9, // High confidence for successful API calls
        processingTime,
        tokensUsed: this.estimateTokens(text + humanizedText),
        success: true
      };

      // Cache the result
      this.cache.set(cacheKey, gemmaResponse);
      
      return gemmaResponse;

    } catch (error) {
      console.error('Gemma API call failed:', error);
      
      return {
        humanizedText: text, // Return original text on failure
        confidence: 0,
        processingTime: Date.now() - startTime,
        tokensUsed: 0,
        success: false
      };
    }
  }

  /**
   * Analyze document structure for intelligent processing
   */
  private analyzeDocumentStructure(text: string): DocumentStructure {
    const paragraphs = text.split(/\n\s*\n/).map((content, index) => {
      const trimmed = content.trim();
      if (!trimmed) {
        return {
          content: '',
          isEmpty: true,
          index,
          sentences: []
        };
      }

      const sentences = this.analyzeSentences(trimmed);
      
      return {
        content: trimmed,
        isEmpty: false,
        index,
        sentences
      };
    });

    const totalSentences = paragraphs.reduce((sum, p) => sum + p.sentences.length, 0);
    const totalWords = paragraphs.reduce((sum, p) => sum + p.content.split(/\s+/).length, 0);
    const averageWordsPerSentence = totalWords / totalSentences;

    // Calculate formality score based on AI-typical words
    const aiWords = ['furthermore', 'moreover', 'comprehensive', 'significant', 'substantial', 'numerous', 'various', 'essential', 'crucial'];
    const aiWordCount = aiWords.reduce((count, word) => {
      const regex = new RegExp(`\\b${word}\\b`, 'gi');
      const matches = text.match(regex);
      return count + (matches ? matches.length : 0);
    }, 0);
    const formalityScore = aiWordCount / totalWords;

    return {
      paragraphs,
      originalFormat: text,
      metadata: {
        totalSentences,
        averageWordsPerSentence,
        formalityScore
      }
    };
  }

  /**
   * Analyze sentences for AI patterns and complexity
   */
  private analyzeSentences(text: string): SentenceInfo[] {
    const sentenceMatches = text.match(/[^.!?]*[.!?]+/g) || [text];
    
    return sentenceMatches.map(sentenceWithPunct => {
      const trimmed = sentenceWithPunct.trim();
      if (!trimmed) return null;

      const punctMatch = trimmed.match(/([.!?]+)$/);
      const punctuation = punctMatch ? punctMatch[1] : '.';
      const sentence = trimmed.replace(/[.!?]+$/, '').trim();

      // Calculate AI pattern score
      const aiWords = ['furthermore', 'moreover', 'comprehensive', 'significant', 'substantial'];
      const aiWordCount = aiWords.filter(word => 
        new RegExp(`\\b${word}\\b`, 'i').test(sentence)
      ).length;
      const words = sentence.split(/\s+/);
      const aiPatternScore = aiWordCount / words.length;

      // Calculate complexity
      const complexity = words.length / 15; // Normalized by average sentence length

      return {
        text: sentence,
        punctuation,
        aiPatternScore,
        complexity,
        needsHumanization: aiPatternScore > 0.1 || complexity > 1.5
      };
    }).filter(Boolean) as SentenceInfo[];
  }

  /**
   * Reconstruct document maintaining original structure
   */
  private reconstructDocument(paragraphs: ParagraphInfo[]): string {
    return paragraphs.map(paragraph => {
      if (paragraph.isEmpty) return '';
      return paragraph.content;
    }).join('\n\n');
  }

  /**
   * Ensure structure preservation after Gemma processing
   */
  private ensureStructurePreservation(processedText: string, originalStructure: DocumentStructure): string {
    // Split processed text into paragraphs
    const processedParagraphs = processedText.split(/\n\s*\n/);
    
    // If paragraph count doesn't match, try to preserve structure
    if (processedParagraphs.length !== originalStructure.paragraphs.length) {
      console.warn('Paragraph structure changed during Gemma processing, attempting to restore...');
      
      // Simple restoration: split by sentence count
      const sentences = processedText.match(/[^.!?]*[.!?]+/g) || [processedText];
      let sentenceIndex = 0;
      
      const restoredParagraphs = originalStructure.paragraphs.map(originalParagraph => {
        if (originalParagraph.isEmpty) return '';
        
        const paragraphSentences = sentences.slice(sentenceIndex, sentenceIndex + originalParagraph.sentences.length);
        sentenceIndex += originalParagraph.sentences.length;
        
        return paragraphSentences.join(' ');
      });
      
      return restoredParagraphs.join('\n\n');
    }
    
    return processedText;
  }

  /**
   * Generate cache key for request
   */
  private getCacheKey(instruction: string, text: string): string {
    const hash = this.simpleHash(instruction + text);
    return `gemma_${hash}`;
  }

  /**
   * Simple hash function for cache keys
   */
  private simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Estimate token usage for cost tracking
   */
  private estimateTokens(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters
    return Math.ceil(text.length / 4);
  }
}

/**
 * Rate limiter for API calls
 */
class APIRateLimiter {
  private requests: number = 0;
  private resetTime: number = Date.now() + 60000; // 1 minute
  private maxRequests: number = 100; // per minute

  async checkLimit(): Promise<boolean> {
    if (Date.now() > this.resetTime) {
      this.requests = 0;
      this.resetTime = Date.now() + 60000;
    }

    if (this.requests >= this.maxRequests) {
      throw new Error('Rate limit exceeded. Please try again later.');
    }

    this.requests++;
    return true;
  }
}

/**
 * Check if Gemma integration is available
 */
export function isGemmaAvailable(): boolean {
  // Check if we're in a server environment and have API keys
  if (typeof process !== 'undefined' && process.env) {
    return !!(process.env.HUGGINGFACE_API_KEY || process.env.GOOGLE_CLOUD_API_KEY);
  }
  return false;
}

/**
 * Estimate processing time based on text length and mode
 */
export function estimateGemmaProcessingTime(text: string, intensity: string): number {
  const baseTime = 500; // Base processing time in ms
  const wordCount = text.split(/\s+/).length;
  const wordMultiplier = wordCount * 2; // 2ms per word
  
  const intensityMultipliers = {
    medium: 1.0,
    heavy: 1.5,
    ultra: 2.0
  };
  
  const multiplier = intensityMultipliers[intensity as keyof typeof intensityMultipliers] || 1.0;
  
  return Math.round((baseTime + wordMultiplier) * multiplier);
}
