import { ProcessingOptions, ProcessingResult } from '@/types';

/**
 * Hugging Face Integration for Ultra-High Quality Humanization
 * Optional enhancement using free Hugging Face models
 */

interface HuggingFaceConfig {
  apiKey?: string;
  model: string;
  maxLength: number;
  temperature: number;
}

interface HuggingFaceResponse {
  generated_text: string;
  score?: number;
}

// Free Hugging Face models for text paraphrasing
const AVAILABLE_MODELS = {
  't5-base': 't5-base',
  'bart-large-cnn': 'facebook/bart-large-cnn',
  'pegasus-xsum': 'google/pegasus-xsum',
  'gpt2': 'gpt2'
};

const DEFAULT_CONFIG: HuggingFaceConfig = {
  model: AVAILABLE_MODELS['t5-base'],
  maxLength: 512,
  temperature: 0.7
};

/**
 * Apply Hugging Face-powered humanization (Ultra mode)
 * Falls back to advanced custom algorithm if API fails
 */
export async function applyHuggingFaceHumanization(
  text: string, 
  options: ProcessingOptions
): Promise<string> {
  // Only apply for heavy mode or if explicitly requested
  if (options.intensity !== 'heavy') {
    return text;
  }

  try {
    // Check if we should use HF integration
    if (!shouldUseHuggingFace(text)) {
      throw new Error('Text not suitable for HF processing');
    }

    // Process with Hugging Face
    const result = await processWithHuggingFace(text, options);
    
    // Post-process the HF result with our custom enhancements
    return postProcessHuggingFaceResult(result, options);
    
  } catch (error) {
    console.warn('Hugging Face processing failed, falling back to custom algorithm:', error);
    
    // Import and use our custom advanced humanizer as fallback
    const { applyAdvancedHumanization } = await import('./advancedHumanizer');
    return applyAdvancedHumanization(text, options);
  }
}

/**
 * Determine if text is suitable for Hugging Face processing
 */
function shouldUseHuggingFace(text: string): boolean {
  // Check text length (HF models have token limits)
  const wordCount = text.split(/\s+/).length;
  if (wordCount > 400) return false; // Too long for free tier
  
  // Check if text is in English (most HF models work best with English)
  const englishPattern = /^[a-zA-Z\s.,!?;:'"()-]+$/;
  if (!englishPattern.test(text.substring(0, 100))) return false;
  
  // Check if we have internet connection (simple check)
  if (typeof navigator !== 'undefined' && !navigator.onLine) return false;
  
  return true;
}

/**
 * Process text using Hugging Face Inference API
 */
async function processWithHuggingFace(
  text: string, 
  options: ProcessingOptions
): Promise<string> {
  const config = { ...DEFAULT_CONFIG };
  
  // Prepare the prompt for paraphrasing
  const prompt = prepareParaphrasingPrompt(text, options);
  
  // Use different models based on text characteristics
  const model = selectOptimalModel(text, options);
  
  const response = await fetch(`https://api-inference.huggingface.co/models/${model}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      // Note: Using free tier without API key for basic access
      // For production, add: 'Authorization': `Bearer ${config.apiKey}`
    },
    body: JSON.stringify({
      inputs: prompt,
      parameters: {
        max_length: config.maxLength,
        temperature: config.temperature,
        do_sample: true,
        top_p: 0.9,
        repetition_penalty: 1.1
      }
    })
  });

  if (!response.ok) {
    throw new Error(`HuggingFace API error: ${response.status}`);
  }

  const result: HuggingFaceResponse[] = await response.json();
  
  if (!result || result.length === 0) {
    throw new Error('No result from HuggingFace API');
  }

  return extractParaphrasedText(result[0].generated_text, text);
}

/**
 * Prepare text prompt for paraphrasing models
 */
function prepareParaphrasingPrompt(text: string, options: ProcessingOptions): string {
  const style = options.style || 'balanced';
  
  // Different prompts for different models and styles
  switch (style) {
    case 'academic':
      return `Rewrite this text in an academic style while maintaining the original meaning: ${text}`;
    case 'casual':
      return `Rewrite this text in a casual, conversational style: ${text}`;
    case 'professional':
      return `Rewrite this text in a professional business style: ${text}`;
    default:
      return `Paraphrase this text to make it sound more natural and human-like: ${text}`;
  }
}

/**
 * Select the optimal model based on text characteristics
 */
function selectOptimalModel(text: string, options: ProcessingOptions): string {
  const wordCount = text.split(/\s+/).length;
  
  // For shorter texts, use T5 (good for paraphrasing)
  if (wordCount < 100) {
    return AVAILABLE_MODELS['t5-base'];
  }
  
  // For medium texts, use BART (excellent for text generation)
  if (wordCount < 200) {
    return AVAILABLE_MODELS['bart-large-cnn'];
  }
  
  // For longer texts, use GPT-2 (good for maintaining context)
  return AVAILABLE_MODELS['gpt2'];
}

/**
 * Extract the paraphrased text from HF response
 */
function extractParaphrasedText(generated: string, original: string): string {
  // Remove the prompt if it's included in the response
  let result = generated;
  
  // Common patterns to clean up
  const cleanupPatterns = [
    /^Rewrite this text.*?:/i,
    /^Paraphrase this text.*?:/i,
    /^.*?while maintaining.*?:/i
  ];
  
  cleanupPatterns.forEach(pattern => {
    result = result.replace(pattern, '').trim();
  });
  
  // If the result is too similar to input or too short, return original
  if (result.length < original.length * 0.5 || 
      calculateSimilarity(result, original) > 0.9) {
    throw new Error('HF result too similar to original');
  }
  
  return result;
}

/**
 * Post-process Hugging Face result with custom enhancements
 */
function postProcessHuggingFaceResult(text: string, options: ProcessingOptions): string {
  let result = text;
  
  // Apply our custom AI pattern removal
  result = removeRemainingAIPatterns(result);
  
  // Add natural contractions
  result = addNaturalContractions(result);
  
  // Ensure proper capitalization and punctuation
  result = cleanupFormatting(result);
  
  return result;
}

/**
 * Remove any remaining AI patterns from HF result
 */
function removeRemainingAIPatterns(text: string): string {
  const aiPatterns = [
    /\b(furthermore|moreover|additionally|consequently)\b/gi,
    /\b(comprehensive|significant|substantial|facilitate)\b/gi,
    /\b(utilize|implement|optimize|enhance)\b/gi
  ];
  
  let result = text;
  
  aiPatterns.forEach(pattern => {
    result = result.replace(pattern, (match) => {
      const replacements: { [key: string]: string } = {
        'furthermore': 'also',
        'moreover': 'plus',
        'additionally': 'and',
        'consequently': 'so',
        'comprehensive': 'complete',
        'significant': 'important',
        'substantial': 'large',
        'facilitate': 'help',
        'utilize': 'use',
        'implement': 'put in place',
        'optimize': 'improve',
        'enhance': 'better'
      };
      
      return replacements[match.toLowerCase()] || match;
    });
  });
  
  return result;
}

/**
 * Add natural contractions to make text more human-like
 */
function addNaturalContractions(text: string): string {
  const contractions = {
    'do not': 'don\'t',
    'does not': 'doesn\'t',
    'cannot': 'can\'t',
    'will not': 'won\'t',
    'it is': 'it\'s',
    'that is': 'that\'s',
    'they are': 'they\'re',
    'we are': 'we\'re'
  };
  
  let result = text;
  
  Object.entries(contractions).forEach(([formal, contraction]) => {
    const regex = new RegExp(`\\b${formal}\\b`, 'gi');
    if (Math.random() < 0.6) { // 60% chance to apply contraction
      result = result.replace(regex, contraction);
    }
  });
  
  return result;
}

/**
 * Clean up formatting and ensure proper structure
 */
function cleanupFormatting(text: string): string {
  let result = text;
  
  // Fix spacing
  result = result.replace(/\s{2,}/g, ' ');
  
  // Ensure proper sentence capitalization
  result = result.replace(/([.!?]\s*)([a-z])/g, (match, punct, letter) => {
    return punct + letter.toUpperCase();
  });
  
  // Ensure first letter is capitalized
  result = result.charAt(0).toUpperCase() + result.slice(1);
  
  return result.trim();
}

/**
 * Calculate similarity between two texts (simple implementation)
 */
function calculateSimilarity(text1: string, text2: string): number {
  const words1 = text1.toLowerCase().split(/\s+/);
  const words2 = text2.toLowerCase().split(/\s+/);
  
  const commonWords = words1.filter(word => words2.includes(word));
  const totalWords = Math.max(words1.length, words2.length);
  
  return commonWords.length / totalWords;
}

/**
 * Check if Hugging Face integration is available and configured
 */
export function isHuggingFaceAvailable(): boolean {
  // Check if we're in a browser environment with fetch support
  if (typeof fetch === 'undefined') return false;
  
  // Check if we have internet connection
  if (typeof navigator !== 'undefined' && !navigator.onLine) return false;
  
  return true;
}

/**
 * Get available Hugging Face models
 */
export function getAvailableModels(): string[] {
  return Object.keys(AVAILABLE_MODELS);
}

/**
 * Estimate processing time for Hugging Face request
 */
export function estimateProcessingTime(text: string): number {
  const wordCount = text.split(/\s+/).length;
  
  // Estimate based on word count (HF API can be slow on free tier)
  if (wordCount < 50) return 3000; // 3 seconds
  if (wordCount < 100) return 5000; // 5 seconds
  if (wordCount < 200) return 8000; // 8 seconds
  
  return 10000; // 10 seconds for longer texts
}
