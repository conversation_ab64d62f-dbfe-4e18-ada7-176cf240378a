/**
 * Trial Management System for <PERSON><PERSON>ayer
 * Handles free trial activation, validation, and expiration logic
 */

export interface TrialStatus {
  isActive: boolean;
  isEligible: boolean;
  isExpired: boolean;
  hasUsedTrial: boolean;
  daysRemaining: number;
  hoursRemaining: number;
  startDate?: Date;
  expiryDate?: Date;
  timeRemaining: number; // in milliseconds
}

export interface UserTrialData {
  id: string;
  email: string;
  tier: string;
  trialStartedAt?: string;
  trialExpiresAt?: string;
  trialUsed: boolean;
  paymentMethodPreference: string;
  bankTransferVerified: boolean;
}

/**
 * Trial configuration constants
 */
export const TRIAL_CONFIG = {
  DURATION_DAYS: 30,
  DURATION_MS: 30 * 24 * 60 * 60 * 1000, // 30 days in milliseconds
  WARNING_DAYS: 3, // Show warning when 3 days remaining
  PREMIUM_FEATURES: [
    'heavy_transformation',
    'batch_processing',
    'priority_support',
    'advanced_analytics'
  ]
};

/**
 * Check if user is eligible for a free trial
 */
export function isTrialEligible(user: UserTrialData): boolean {
  // User must not have used trial before
  if (user.trialUsed) return false;

  // User must not already be premium
  if (user.tier === 'premium') return false;

  // User must not have an active trial - check directly without calling getTrialStatus
  if (user.trialStartDate) {
    const now = new Date();
    const trialStart = new Date(user.trialStartDate);
    const trialEnd = new Date(trialStart.getTime() + TRIAL_CONFIG.DURATION_MS);

    // If trial is still active, user is not eligible for another trial
    if (now < trialEnd) return false;
  }

  return true;
}

/**
 * Get comprehensive trial status for a user
 */
export function getTrialStatus(user: UserTrialData): TrialStatus {
  const now = new Date();
  
  // Default status for users without trial
  const defaultStatus: TrialStatus = {
    isActive: false,
    isEligible: isTrialEligible(user),
    isExpired: false,
    hasUsedTrial: user.trialUsed,
    daysRemaining: 0,
    hoursRemaining: 0,
    timeRemaining: 0
  };
  
  // If user hasn't started trial, return default
  if (!user.trialStartedAt || !user.trialExpiresAt) {
    return defaultStatus;
  }
  
  const startDate = new Date(user.trialStartedAt);
  const expiryDate = new Date(user.trialExpiresAt);
  const timeRemaining = expiryDate.getTime() - now.getTime();
  
  const isActive = timeRemaining > 0;
  const isExpired = timeRemaining <= 0 && user.trialUsed;
  
  return {
    isActive,
    isEligible: false, // Not eligible if trial has been started
    isExpired,
    hasUsedTrial: user.trialUsed,
    daysRemaining: Math.max(0, Math.ceil(timeRemaining / (1000 * 60 * 60 * 24))),
    hoursRemaining: Math.max(0, Math.ceil(timeRemaining / (1000 * 60 * 60))),
    timeRemaining: Math.max(0, timeRemaining),
    startDate,
    expiryDate
  };
}

/**
 * Check if user has premium access (including active trial)
 */
export function hasPremiumAccess(user: UserTrialData): boolean {
  // Check if user has premium tier
  if (user.tier === 'premium') return true;
  
  // Check if user has active trial
  const trialStatus = getTrialStatus(user);
  return trialStatus.isActive;
}

/**
 * Check if user can access specific premium feature
 */
export function canAccessFeature(user: UserTrialData, feature: string): boolean {
  // Check if feature is premium
  if (!TRIAL_CONFIG.PREMIUM_FEATURES.includes(feature)) {
    return true; // Non-premium features are always accessible
  }
  
  return hasPremiumAccess(user);
}

/**
 * Generate trial activation data
 */
export function generateTrialActivation(): { startDate: Date; expiryDate: Date } {
  const startDate = new Date();
  const expiryDate = new Date(startDate.getTime() + TRIAL_CONFIG.DURATION_MS);
  
  return { startDate, expiryDate };
}

/**
 * Check if trial is in warning period (last 3 days)
 */
export function isTrialInWarningPeriod(user: UserTrialData): boolean {
  const trialStatus = getTrialStatus(user);
  return trialStatus.isActive && trialStatus.daysRemaining <= TRIAL_CONFIG.WARNING_DAYS;
}

/**
 * Get trial warning message
 */
export function getTrialWarningMessage(user: UserTrialData): string | null {
  const trialStatus = getTrialStatus(user);
  
  if (!trialStatus.isActive) return null;
  
  if (trialStatus.daysRemaining === 0) {
    return `Your free trial expires in ${trialStatus.hoursRemaining} hours. Upgrade now to continue using premium features.`;
  }
  
  if (trialStatus.daysRemaining <= TRIAL_CONFIG.WARNING_DAYS) {
    return `Your free trial expires in ${trialStatus.daysRemaining} days. Upgrade now to avoid interruption.`;
  }
  
  return null;
}

/**
 * Format remaining time for display
 */
export function formatRemainingTime(timeRemaining: number): string {
  const days = Math.floor(timeRemaining / (1000 * 60 * 60 * 24));
  const hours = Math.floor((timeRemaining % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  
  if (days > 0) {
    return `${days} day${days !== 1 ? 's' : ''}`;
  } else if (hours > 0) {
    return `${hours} hour${hours !== 1 ? 's' : ''}`;
  } else {
    return 'Less than 1 hour';
  }
}

/**
 * Generate unique reference code for bank transfers
 */
export function generateBankTransferReference(): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  return `GL${timestamp}${random}`.toUpperCase();
}

/**
 * Validate bank transfer reference format
 */
export function isValidBankTransferReference(reference: string): boolean {
  // Format: GL + timestamp + random (e.g., GL1A2B3C4D5E6F)
  const pattern = /^GL[A-Z0-9]{10,}$/;
  return pattern.test(reference);
}
