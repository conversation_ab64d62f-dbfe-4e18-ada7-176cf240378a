import { DefaultSession } from 'next-auth';

declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      tier: string;
      credits: number;
      trialStartedAt?: string | null;
      trialExpiresAt?: string | null;
      trialUsed: boolean;
      paymentMethodPreference: string;
      bankTransferVerified: boolean;
    } & DefaultSession['user'];
  }
  
  interface JWT {
    id: string;
    tier: string;
    credits: number;
    trialStartedAt?: string | null;
    trialExpiresAt?: string | null;
    trialUsed: boolean;
    paymentMethodPreference: string;
    bankTransferVerified: boolean;
  }
}
